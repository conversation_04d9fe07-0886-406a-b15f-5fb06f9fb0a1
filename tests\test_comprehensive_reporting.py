"""
Comprehensive Testing Suite for Enhanced Reporting System
========================================================

Tests all chart types, export formats, and ensures professional quality.
"""

import unittest
import tempfile
import shutil
from pathlib import Path
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Import the services and models
import sys
sys.path.append('.')

from services.report_service import ReportGenerationService
from services.export_service import ExportService
from components.charts.chart_factory import ChartFactory
from models.client_profile import ClientProfile
from models.project_assumptions import EnhancedProjectAssumptions


class TestComprehensiveReporting(unittest.TestCase):
    """Test suite for comprehensive reporting system."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.report_service = ReportGenerationService()
        self.export_service = ExportService()
        self.chart_factory = ChartFactory()
        
        # Create test client profile
        self.client_profile = ClientProfile(
            company_name="Test Energy Corp",
            client_name="<PERSON>",
            project_name="Test Solar Project",
            project_location="Test Location",
            consultant="Test Consultant",
            consultant_website="https://test.com",
            tagline="Professional Testing",
            report_date=datetime.now().strftime("%Y-%m-%d")
        )
        
        # Create test project assumptions
        self.assumptions = EnhancedProjectAssumptions(
            capacity_mw=100.0,
            technology_type="Solar PV",
            capex_meur=80.0,
            opex_meur_per_year=2.0,
            project_life_years=25,
            equity_percentage=0.3,
            debt_interest_rate=0.05,
            discount_rate=0.08
        )
        
        # Create sample financial results
        self.financial_results = {
            'kpis': {
                'IRR_project': 0.125,
                'IRR_equity': 0.18,
                'NPV_project': 15000000,
                'NPV_equity': 8000000,
                'LCOE_eur_kwh': 0.042,
                'Min_DSCR': 1.35,
                'Payback_Period': 8.5
            },
            'cashflow': pd.DataFrame({
                'Year': range(1, 26),
                'Free_Cash_Flow_Project': np.random.normal(2000000, 500000, 25),
                'Free_Cash_Flow_Equity': np.random.normal(1000000, 300000, 25),
                'Operating_Cash_Flow': np.random.normal(3000000, 400000, 25),
                'EBITDA': np.random.normal(3500000, 450000, 25),
                'DSCR': np.random.normal(1.4, 0.2, 25)
            })
        }
        
        # Create sample analysis results
        self.analysis_results = {
            'financial': self.financial_results,
            'sensitivity': pd.DataFrame(
                np.random.randn(5, 5) * 5,
                index=['CAPEX', 'OPEX', 'Tariff', 'Capacity Factor', 'Discount Rate'],
                columns=['-20%', '-10%', '0%', '+10%', '+20%']
            ),
            'monte_carlo': {
                'simulation_results': np.random.normal(15000000, 3000000, 1000),
                'statistics': {'mean': 15000000, 'std': 3000000}
            },
            'scenarios': {
                'Base Case': {'IRR': 0.125, 'NPV': 15000000},
                'Optimistic': {'IRR': 0.15, 'NPV': 20000000},
                'Pessimistic': {'IRR': 0.08, 'NPV': 5000000}
            }
        }
    
    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_chart_generation(self):
        """Test all chart types generate successfully."""
        print("\n🧪 Testing Chart Generation...")
        
        # Test basic charts
        kpi_data = {'IRR': 12.5, 'NPV': 15.2, 'DSCR': 1.8, 'LCOE': 4.2}
        ui_component, chart_bytes = self.chart_factory.create_and_export_bar_chart(
            data=kpi_data,
            title='Test KPI Chart'
        )
        self.assertIsNotNone(chart_bytes)
        self.assertGreater(len(chart_bytes), 1000)  # Should be a substantial image
        print("✅ Basic bar chart generation")
        
        # Test sensitivity heatmap
        sensitivity_df = pd.DataFrame(
            np.random.randn(5, 5),
            index=['CAPEX', 'OPEX', 'Tariff', 'Capacity', 'Discount Rate'],
            columns=['-20%', '-10%', '0%', '+10%', '+20%']
        )
        ui_component, chart_bytes = self.chart_factory.create_sensitivity_heatmap(
            sensitivity_data=sensitivity_df,
            title='Test Sensitivity Analysis'
        )
        self.assertIsNotNone(chart_bytes)
        print("✅ Sensitivity heatmap generation")
        
        # Test Monte Carlo distribution
        simulation_results = np.random.normal(10000000, 2000000, 1000)
        ui_component, chart_bytes = self.chart_factory.create_monte_carlo_distribution(
            simulation_results=simulation_results,
            title='Test Monte Carlo Analysis'
        )
        self.assertIsNotNone(chart_bytes)
        print("✅ Monte Carlo distribution chart")
        
        # Test tornado diagram
        tornado_data = {
            'CAPEX': {'low': -15, 'high': 12},
            'OPEX': {'low': -8, 'high': 10},
            'Tariff': {'low': -20, 'high': 25}
        }
        ui_component, chart_bytes = self.chart_factory.create_tornado_diagram(
            sensitivity_data=tornado_data,
            title='Test Tornado Diagram'
        )
        self.assertIsNotNone(chart_bytes)
        print("✅ Tornado diagram generation")
        
        print("🎉 All chart types generated successfully!")
    
    def test_export_formats(self):
        """Test all export formats work correctly."""
        print("\n🧪 Testing Export Formats...")
        
        # Test Excel export
        try:
            excel_file = self.export_service.export_excel_report(
                client_profile=self.client_profile,
                assumptions=self.assumptions,
                financial_results=self.financial_results,
                output_dir={'reports_dir': self.temp_dir}
            )
            self.assertTrue(excel_file.exists())
            print("✅ Excel export successful")
        except Exception as e:
            print(f"⚠️ Excel export failed: {e}")
        
        # Test HTML export
        try:
            html_file = self.export_service.export_html_report(
                client_profile=self.client_profile,
                assumptions=self.assumptions,
                financial_results=self.financial_results,
                output_dir={'reports_dir': self.temp_dir}
            )
            self.assertTrue(html_file.exists())
            print("✅ HTML export successful")
        except Exception as e:
            print(f"⚠️ HTML export failed: {e}")
        
        # Test JSON export
        try:
            json_file = self.export_service.export_json_data(
                client_profile=self.client_profile,
                assumptions=self.assumptions,
                financial_results=self.financial_results,
                output_dir={'reports_dir': self.temp_dir}
            )
            self.assertTrue(json_file.exists())
            print("✅ JSON export successful")
        except Exception as e:
            print(f"⚠️ JSON export failed: {e}")
        
        print("🎉 Core export formats tested!")
    
    def test_comprehensive_report_generation(self):
        """Test the complete comprehensive report generation."""
        print("\n🧪 Testing Comprehensive Report Generation...")
        
        try:
            # Mock progress callback
            progress_messages = []
            def progress_callback(progress: float, message: str):
                progress_messages.append((progress, message))
            
            # Generate comprehensive report
            results = self.report_service.generate_comprehensive_report(
                client_profile=self.client_profile,
                assumptions=self.assumptions,
                export_formats=['excel', 'html', 'json'],
                progress_callback=progress_callback
            )
            
            # Verify results structure
            self.assertIn('generated_files', results)
            self.assertIn('analysis_results', results)
            self.assertIn('charts', results)
            self.assertIn('summary', results)
            
            # Verify progress tracking
            self.assertGreater(len(progress_messages), 5)
            self.assertEqual(progress_messages[-1][0], 100)  # Should end at 100%
            
            # Verify charts were generated
            self.assertGreater(len(results['charts']), 5)  # Should have multiple charts
            
            # Verify files were generated
            self.assertGreater(len(results['generated_files']), 2)
            
            print(f"✅ Generated {len(results['charts'])} charts")
            print(f"✅ Generated {len(results['generated_files'])} files")
            print(f"✅ Tracked {len(progress_messages)} progress updates")
            print("🎉 Comprehensive report generation successful!")
            
        except Exception as e:
            print(f"❌ Comprehensive report generation failed: {e}")
            raise
    
    def test_executive_summary_generation(self):
        """Test executive summary generation with intelligent insights."""
        print("\n🧪 Testing Executive Summary Generation...")
        
        try:
            summary = self.report_service.generate_executive_summary(self.analysis_results)
            
            # Verify summary contains key sections
            self.assertIn("EXECUTIVE SUMMARY", summary)
            self.assertIn("KEY FINANCIAL PERFORMANCE", summary)
            self.assertIn("INVESTMENT RECOMMENDATION", summary)
            self.assertIn("NEXT STEPS", summary)
            
            # Verify financial metrics are included
            self.assertIn("Project IRR", summary)
            self.assertIn("NPV Project", summary)
            self.assertIn("LCOE", summary)
            self.assertIn("Min DSCR", summary)
            
            print("✅ Executive summary generated with all sections")
            print("✅ Financial metrics properly formatted")
            print("✅ Investment recommendations included")
            print("🎉 Executive summary generation successful!")
            
        except Exception as e:
            print(f"❌ Executive summary generation failed: {e}")
            raise


if __name__ == '__main__':
    print("🚀 Starting Comprehensive Reporting System Tests")
    print("=" * 60)
    
    # Run the tests
    unittest.main(verbosity=2)
