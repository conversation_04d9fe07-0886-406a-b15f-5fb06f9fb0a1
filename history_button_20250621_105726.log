2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] INFO: 🧪 History logging system initialized and ready
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:57 in setup_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] DEBUG: 🔍 Debug logging is working
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:58 in setup_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] INFO: 🔍 History button clicked by user
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:70 in test_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] DEBUG: Client profile complete: True
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:71 in test_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] DEBUG: Client profile data: company='Test Company', client='Test User', project='Test Project'
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:72 in test_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] INFO: 📁 Opening history for project ID: 'Test_Company'
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:73 in test_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] INFO: 🔄 Starting history dialog for project: 'Test_Company'
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:74 in test_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] DEBUG: 📱 Adding loading dialog to page overlay
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:75 in test_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] DEBUG: 🔧 Importing enhanced integration service
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:76 in test_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] DEBUG: ✅ Enhanced service obtained: EnhancedIntegrationService
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:77 in test_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] DEBUG: 📊 Querying project versions for: 'Test_Company'
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:78 in test_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] INFO: 📋 Found 3 version(s) for project 'Test_Company'
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:79 in test_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] DEBUG:    Version 3: created=2025-06-21 10:48:04, size=1024 bytes, comment='Latest version'
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:80 in test_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] DEBUG:    Version 2: created=2025-06-21 10:47:30, size=987 bytes, comment='Modified parameters'
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:81 in test_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] DEBUG:    Version 1: created=2025-06-21 10:46:15, size=945 bytes, comment='Initial setup'
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:82 in test_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] INFO: 🎨 Creating history dialog with version list
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:83 in test_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] INFO: 📱 Adding history dialog to page overlay and showing
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:84 in test_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] INFO: ✅ History dialog displayed successfully
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:85 in test_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] INFO: 🔄 User requested restore of version 2 for project 'Test_Company'
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:88 in test_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] INFO: ✅ User confirmed restore of version 2
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:89 in test_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] INFO: 🔄 Starting restore operation for version 2 of project 'Test_Company'
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:90 in test_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] DEBUG: 🔧 Getting enhanced integration service for restore
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:91 in test_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] DEBUG: 📊 Loading project data for version 2
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:92 in test_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] INFO: ✅ Successfully loaded project data for version 2
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:93 in test_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] DEBUG: 🔄 Restoring client profile data
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:94 in test_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] DEBUG: ✅ Client form updated with restored data
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:95 in test_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] DEBUG: 🔄 Restoring project assumptions data
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:96 in test_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] DEBUG: ✅ Parameters form updated with restored data
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:97 in test_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] INFO: 🎉 Version 2 restored successfully without errors
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:98 in test_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] INFO: 🔒 Closing history dialog
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:99 in test_history_logging()

2025-06-21 10:57:26 [views.project_setup_view.ProjectSetupView.History] DEBUG: ✅ History dialog closed successfully
File: D:\pro projects\flet\Hiel RnE Model (v3)\setup_history_logging.py:100 in test_history_logging()

