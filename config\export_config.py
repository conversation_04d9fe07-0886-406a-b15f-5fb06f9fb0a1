"""
Export Configuration
====================

Configuration settings for export functionality.
"""

from dataclasses import dataclass
from typing import Dict, Any, List
from pathlib import Path


@dataclass
class ExportConfig:
    """Export configuration settings."""
    
    # Default export formats
    default_formats: List[str] = None
    
    # File naming
    include_timestamp: bool = True
    include_client_name: bool = True
    timestamp_format: str = "%Y%m%d_%H%M%S"
    
    # Excel export settings
    excel_include_charts: bool = True
    excel_include_metadata: bool = True
    excel_sheet_names: Dict[str, str] = None
    
    # DOCX export settings
    docx_include_charts: bool = True
    docx_include_validation: bool = True
    docx_template_path: str = ""
    
    # HTML export settings
    html_include_css: bool = True
    html_responsive: bool = True
    html_include_charts: bool = True
    
    # Chart export settings - Enhanced 2025
    chart_format: str = "PNG"
    chart_dpi: int = 300  # High resolution for professional reports
    chart_width: int = 1200
    chart_height: int = 800
    chart_background_color: str = "white"
    chart_transparent_background: bool = False

    # Chart-specific settings
    chart_types_enabled: Dict[str, bool] = None
    chart_color_scheme: str = "professional"  # professional, colorful, monochrome
    chart_font_family: str = "Arial"
    chart_font_size_title: int = 16
    chart_font_size_labels: int = 12
    chart_font_size_legend: int = 10

    # Financial chart specific settings
    currency_symbol: str = "€"
    number_format_large: str = "M"  # M for millions, K for thousands
    percentage_decimals: int = 1

    # Chart quality settings
    chart_quality_level: str = "high"  # low, medium, high, ultra
    chart_compression: bool = False
    chart_watermark: bool = False
    chart_watermark_text: str = "Agevolami SRL"
    
    # Directory structure
    create_date_folders: bool = True
    create_client_folders: bool = True
    folder_structure: str = "{date}/{client_name}"
    
    def __post_init__(self):
        """Initialize default values."""
        if self.default_formats is None:
            self.default_formats = ["Excel", "DOCX", "HTML"]

        if self.excel_sheet_names is None:
            self.excel_sheet_names = {
                'assumptions': 'Project Assumptions',
                'cashflow': 'Cashflow Analysis',
                'kpis': 'Key Performance Indicators',
                'sensitivity': 'Sensitivity Analysis',
                'monte_carlo': 'Monte Carlo Results',
                'scenarios': 'Scenario Analysis',
                'validation': 'Model Validation',
                'metadata': 'Project Metadata'
            }

        if self.chart_types_enabled is None:
            self.chart_types_enabled = {
                'financial_kpis': True,
                'cash_flow_analysis': True,
                'dcf_waterfall': True,
                'sensitivity_tornado': True,
                'monte_carlo_histogram': True,
                'scenario_comparison': True,
                'location_comparison': True,
                'benchmark_comparison': True,
                'risk_assessment': True,
                'npv_breakdown': True
            }
    
    def get_excel_settings(self) -> Dict[str, Any]:
        """Get Excel export settings."""
        return {
            'include_charts': self.excel_include_charts,
            'include_metadata': self.excel_include_metadata,
            'sheet_names': self.excel_sheet_names
        }
    
    def get_docx_settings(self) -> Dict[str, Any]:
        """Get DOCX export settings."""
        return {
            'include_charts': self.docx_include_charts,
            'include_validation': self.docx_include_validation,
            'template_path': self.docx_template_path
        }
    
    def get_html_settings(self) -> Dict[str, Any]:
        """Get HTML export settings."""
        return {
            'include_css': self.html_include_css,
            'responsive': self.html_responsive,
            'include_charts': self.html_include_charts
        }
    
    def get_chart_settings(self) -> Dict[str, Any]:
        """Get comprehensive chart export settings."""
        return {
            'format': self.chart_format,
            'dpi': self.chart_dpi,
            'width': self.chart_width,
            'height': self.chart_height,
            'background_color': self.chart_background_color,
            'transparent_background': self.chart_transparent_background,
            'color_scheme': self.chart_color_scheme,
            'font_family': self.chart_font_family,
            'font_size_title': self.chart_font_size_title,
            'font_size_labels': self.chart_font_size_labels,
            'font_size_legend': self.chart_font_size_legend,
            'currency_symbol': self.currency_symbol,
            'number_format_large': self.number_format_large,
            'percentage_decimals': self.percentage_decimals,
            'quality_level': self.chart_quality_level,
            'compression': self.chart_compression,
            'watermark': self.chart_watermark,
            'watermark_text': self.chart_watermark_text,
            'enabled_types': self.chart_types_enabled
        }
    
    def get_filename_template(self, file_type: str) -> str:
        """Get filename template for file type."""
        templates = {
            'excel': 'financial_model_{timestamp}.xlsx',
            'docx': 'financial_report_{timestamp}.docx',
            'html': 'financial_report_{timestamp}.html',
            'json': 'financial_data_{timestamp}.json'
        }
        return templates.get(file_type.lower(), f'{file_type.lower()}_{{{self.timestamp_format}}}.{file_type.lower()}')
    
    def get_folder_structure_template(self) -> str:
        """Get folder structure template."""
        return self.folder_structure
    
    def should_create_date_folders(self) -> bool:
        """Check if date folders should be created."""
        return self.create_date_folders
    
    def should_create_client_folders(self) -> bool:
        """Check if client folders should be created."""
        return self.create_client_folders
    
    def get_timestamp_format(self) -> str:
        """Get timestamp format."""
        return self.timestamp_format

    def get_chart_dpi_by_quality(self) -> int:
        """Get DPI based on quality level."""
        quality_dpi_map = {
            'low': 72,
            'medium': 150,
            'high': 300,
            'ultra': 600
        }
        return quality_dpi_map.get(self.chart_quality_level, self.chart_dpi)

    def get_chart_dimensions_by_type(self, chart_type: str) -> tuple:
        """Get chart dimensions based on chart type."""
        dimension_map = {
            'financial_kpis': (1200, 800),
            'cash_flow_analysis': (1400, 900),
            'dcf_waterfall': (1600, 1000),
            'sensitivity_tornado': (1200, 800),
            'monte_carlo_histogram': (1000, 700),
            'scenario_comparison': (1200, 800),
            'location_comparison': (1300, 850),
            'benchmark_comparison': (1100, 750),
            'risk_assessment': (1000, 600),
            'npv_breakdown': (1400, 900)
        }
        return dimension_map.get(chart_type, (self.chart_width, self.chart_height))

    def get_color_palette(self) -> Dict[str, str]:
        """Get color palette based on color scheme."""
        color_schemes = {
            'professional': {
                'primary': '#2E86AB',
                'secondary': '#A23B72',
                'success': '#F18F01',
                'warning': '#C73E1D',
                'danger': '#C73E1D',  # Same as warning for professional scheme
                'info': '#5D737E',
                'light': '#F5F5F5',
                'dark': '#2C3E50'
            },
            'colorful': {
                'primary': '#3498DB',
                'secondary': '#E74C3C',
                'success': '#2ECC71',
                'warning': '#F39C12',
                'danger': '#E74C3C',  # Red color for danger
                'info': '#9B59B6',
                'light': '#ECF0F1',
                'dark': '#34495E'
            },
            'monochrome': {
                'primary': '#2C3E50',
                'secondary': '#7F8C8D',
                'success': '#95A5A6',
                'warning': '#BDC3C7',
                'danger': '#7F8C8D',  # Dark grey for monochrome danger
                'info': '#34495E',
                'light': '#ECF0F1',
                'dark': '#2C3E50'
            }
        }
        return color_schemes.get(self.chart_color_scheme, color_schemes['professional'])

    def is_chart_type_enabled(self, chart_type: str) -> bool:
        """Check if a specific chart type is enabled."""
        return self.chart_types_enabled.get(chart_type, True)

    def get_chart_filename_template(self, chart_type: str) -> str:
        """Get filename template for specific chart type."""
        templates = {
            'financial_kpis': 'financial_kpis_{timestamp}.png',
            'cash_flow_analysis': 'cash_flow_analysis_{timestamp}.png',
            'dcf_waterfall': 'dcf_waterfall_{timestamp}.png',
            'sensitivity_tornado': 'sensitivity_tornado_{timestamp}.png',
            'monte_carlo_histogram': 'monte_carlo_histogram_{timestamp}.png',
            'scenario_comparison': 'scenario_comparison_{timestamp}.png',
            'location_comparison': 'location_comparison_{timestamp}.png',
            'benchmark_comparison': 'benchmark_comparison_{timestamp}.png',
            'risk_assessment': 'risk_assessment_{timestamp}.png',
            'npv_breakdown': 'npv_breakdown_{timestamp}.png'
        }
        return templates.get(chart_type, f'{chart_type}_{{{self.timestamp_format}}}.png')

    def get_export_quality_settings(self) -> Dict[str, Any]:
        """Get quality-specific export settings."""
        quality_settings = {
            'low': {
                'dpi': 72,
                'compression': True,
                'optimize': True,
                'progressive': False
            },
            'medium': {
                'dpi': 150,
                'compression': True,
                'optimize': True,
                'progressive': True
            },
            'high': {
                'dpi': 300,
                'compression': False,
                'optimize': False,
                'progressive': True
            },
            'ultra': {
                'dpi': 600,
                'compression': False,
                'optimize': False,
                'progressive': True
            }
        }
        return quality_settings.get(self.chart_quality_level, quality_settings['high'])
