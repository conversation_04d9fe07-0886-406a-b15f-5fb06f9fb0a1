"""
Application Configuration
=========================

Main application configuration settings.
"""

from dataclasses import dataclass
from typing import Dict, Any, Optional
from pathlib import Path
import json
import os


@dataclass
class AppConfig:
    """Application configuration settings - Updated for 2025."""

    # Application metadata - 2025 Edition
    app_name: str = "Enhanced Financial Model"
    app_version: str = "3.0.0"
    app_year: str = "2025"
    company_name: str = "Agevolami SRL"
    company_website: str = "www.agevolami.it & www.agevolami.ma"
    company_tagline: str = "Your gateway to cross-border opportunities and exponential growth"
    
    # File paths
    data_directory: str = "data"
    output_directory: str = "outputs"
    config_directory: str = "config"
    
    # Default values
    default_currency: str = "EUR"
    default_language: str = "EN"
    default_theme: str = "light"
    
    # Export settings
    export_formats: list = None
    include_charts_in_export: bool = True
    auto_open_exports: bool = False
    
    # Performance settings
    max_monte_carlo_simulations: int = 10000
    default_monte_carlo_simulations: int = 1000
    chart_dpi: int = 100
    
    # UI settings
    show_tooltips: bool = True
    auto_save: bool = True
    chart_animation: bool = True
    
    def __post_init__(self):
        """Initialize default values after creation."""
        if self.export_formats is None:
            self.export_formats = ["Excel", "DOCX", "HTML", "JSON"]

        # Update output directory to use Documents folder if using default
        if self.output_directory == "outputs":
            self.output_directory = self._get_documents_output_path()
    
    @classmethod
    def load_from_file(cls, config_path: Path) -> 'AppConfig':
        """Load configuration from file."""
        if config_path.exists():
            try:
                with open(config_path, 'r') as f:
                    data = json.load(f)
                return cls(**data)
            except Exception:
                # Return default config if loading fails
                return cls()
        return cls()
    
    def save_to_file(self, config_path: Path):
        """Save configuration to file."""
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_path, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'app_name': self.app_name,
            'app_version': self.app_version,
            'company_name': self.company_name,
            'company_website': self.company_website,
            'company_tagline': self.company_tagline,
            'data_directory': self.data_directory,
            'output_directory': self.output_directory,
            'config_directory': self.config_directory,
            'default_currency': self.default_currency,
            'default_language': self.default_language,
            'default_theme': self.default_theme,
            'export_formats': self.export_formats,
            'include_charts_in_export': self.include_charts_in_export,
            'auto_open_exports': self.auto_open_exports,
            'max_monte_carlo_simulations': self.max_monte_carlo_simulations,
            'default_monte_carlo_simulations': self.default_monte_carlo_simulations,
            'chart_dpi': self.chart_dpi,
            'show_tooltips': self.show_tooltips,
            'auto_save': self.auto_save,
            'chart_animation': self.chart_animation
        }
    
    def get_data_path(self) -> Path:
        """Get data directory path."""
        return Path(self.data_directory)
    
    def get_output_path(self) -> Path:
        """Get output directory path."""
        return Path(self.output_directory)
    
    def get_config_path(self) -> Path:
        """Get config directory path."""
        return Path(self.config_directory)
    
    def update_setting(self, key: str, value: Any):
        """Update a configuration setting."""
        if hasattr(self, key):
            setattr(self, key, value)
    
    def get_company_info(self) -> Dict[str, str]:
        """Get company information."""
        return {
            'name': self.company_name,
            'website': self.company_website,
            'tagline': self.company_tagline
        }
    
    def get_export_settings(self) -> Dict[str, Any]:
        """Get export settings."""
        return {
            'formats': self.export_formats,
            'include_charts': self.include_charts_in_export,
            'auto_open': self.auto_open_exports,
            'chart_dpi': self.chart_dpi
        }
    
    def get_ui_settings(self) -> Dict[str, Any]:
        """Get UI settings."""
        return {
            'theme': self.default_theme,
            'language': self.default_language,
            'show_tooltips': self.show_tooltips,
            'auto_save': self.auto_save,
            'chart_animation': self.chart_animation
        }

    def _get_documents_output_path(self) -> str:
        """Get the output path in Windows Documents folder."""
        try:
            # Get Windows Documents folder
            documents_folder = Path.home() / "Documents"

            # Create application folder in Documents
            app_folder = documents_folder / "Enhanced Financial Model"
            output_folder = app_folder / "outputs"

            # Create the directory if it doesn't exist
            output_folder.mkdir(parents=True, exist_ok=True)

            return str(output_folder)

        except Exception:
            # Fallback to current directory if Documents folder access fails
            return "outputs"

    def get_documents_path(self) -> Path:
        """Get the main application folder in Documents."""
        try:
            documents_folder = Path.home() / "Documents"
            app_folder = documents_folder / "Enhanced Financial Model"
            app_folder.mkdir(parents=True, exist_ok=True)
            return app_folder
        except Exception:
            return Path(".")
