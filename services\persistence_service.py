"""
Data Persistence Service
========================

Advanced data persistence with versioning, backup, and recovery capabilities.
"""

import sqlite3
import json
import gzip
import pickle
import hashlib
import logging
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timedelta
from pathlib import Path
from dataclasses import dataclass, asdict
import threading
import shutil

from models.client_profile import ClientProfile
from models.project_assumptions import EnhancedProjectAssumptions


@dataclass
class ProjectData:
    """Project data container with metadata."""
    id: str
    name: str
    client_profile: Dict[str, Any]
    project_assumptions: Dict[str, Any]
    financial_results: Optional[Dict[str, Any]] = None
    created_at: datetime = None
    modified_at: datetime = None
    version: int = 1
    tags: List[str] = None
    description: str = ""
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.modified_at is None:
            self.modified_at = datetime.now()
        if self.tags is None:
            self.tags = []


@dataclass
class ProjectVersion:
    """Project version information."""
    project_id: str
    version: int
    data: bytes  # Compressed project data
    checksum: str
    created_at: datetime
    comment: str = ""
    file_size: int = 0


class DataPersistenceService:
    """Advanced data persistence service with versioning and backup."""
    
    def __init__(self, db_path: str = "data/projects.db", backup_dir: str = "data/backups"):
        self.logger = logging.getLogger(__name__)
        self.db_path = Path(db_path)
        self.backup_dir = Path(backup_dir)
        self._lock = threading.RLock()
        
        # Ensure directories exist
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self.backup_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize database
        self._initialize_database()
        
        # Setup automatic backup
        self._setup_auto_backup()
        
        self.logger.info("Data persistence service initialized")
    
    def _initialize_database(self):
        """Initialize SQLite database with tables."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS projects (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    client_profile TEXT NOT NULL,
                    project_assumptions TEXT NOT NULL,
                    financial_results TEXT,
                    created_at TIMESTAMP,
                    modified_at TIMESTAMP,
                    version INTEGER DEFAULT 1,
                    tags TEXT DEFAULT '[]',
                    description TEXT DEFAULT '',
                    is_deleted BOOLEAN DEFAULT FALSE
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS project_versions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    project_id TEXT NOT NULL,
                    version INTEGER NOT NULL,
                    data BLOB NOT NULL,
                    checksum TEXT NOT NULL,
                    created_at TIMESTAMP,
                    comment TEXT DEFAULT '',
                    file_size INTEGER DEFAULT 0,
                    FOREIGN KEY (project_id) REFERENCES projects (id),
                    UNIQUE(project_id, version)
                )
            """)
            
            conn.execute("""
                CREATE TABLE IF NOT EXISTS recent_projects (
                    project_id TEXT PRIMARY KEY,
                    last_accessed TIMESTAMP,
                    access_count INTEGER DEFAULT 1,
                    FOREIGN KEY (project_id) REFERENCES projects (id)
                )
            """)
            
            # Create indexes for better performance
            conn.execute("CREATE INDEX IF NOT EXISTS idx_projects_modified ON projects(modified_at)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_versions_project ON project_versions(project_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_recent_accessed ON recent_projects(last_accessed)")
            
            conn.commit()
    
    def save_project(self, project: ProjectData, auto_version: bool = True) -> bool:
        """Save project with automatic versioning."""
        try:
            with self._lock:
                with sqlite3.connect(self.db_path) as conn:
                    # Check if project exists
                    existing = conn.execute(
                        "SELECT version FROM projects WHERE id = ? AND is_deleted = FALSE", 
                        (project.id,)
                    ).fetchone()
                    
                    if existing:
                        project.version = existing[0] + 1 if auto_version else existing[0]
                        project.modified_at = datetime.now()
                        
                        # Update existing project
                        conn.execute("""
                            UPDATE projects 
                            SET name = ?, client_profile = ?, project_assumptions = ?, 
                                financial_results = ?, modified_at = ?, version = ?, 
                                tags = ?, description = ?
                            WHERE id = ?
                        """, (
                            project.name,
                            json.dumps(project.client_profile),
                            json.dumps(project.project_assumptions),
                            json.dumps(project.financial_results) if project.financial_results else None,
                            project.modified_at,
                            project.version,
                            json.dumps(project.tags),
                            project.description,
                            project.id
                        ))
                    else:
                        # Insert new project
                        project.created_at = datetime.now()
                        project.modified_at = project.created_at
                        
                        conn.execute("""
                            INSERT INTO projects 
                            (id, name, client_profile, project_assumptions, financial_results,
                             created_at, modified_at, version, tags, description)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            project.id,
                            project.name,
                            json.dumps(project.client_profile),
                            json.dumps(project.project_assumptions),
                            json.dumps(project.financial_results) if project.financial_results else None,
                            project.created_at,
                            project.modified_at,
                            project.version,
                            json.dumps(project.tags),
                            project.description
                        ))
                    
                    # Save version if auto-versioning is enabled
                    if auto_version:
                        self._save_version(conn, project)
                    
                    # Update recent projects
                    self._update_recent_projects(conn, project.id)
                    
                    conn.commit()
                    
            self.logger.info(f"Project {project.id} saved successfully (version {project.version})")
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving project {project.id}: {e}")
            return False
    
    def save_project_version(self, project_id: str, project_data: Dict[str, Any], comment: str = "") -> str:
        """Save a new version of the project."""
        try:
            with self._lock:
                # Load current project to get version number
                current_project = self.load_project(project_id)
                if not current_project:
                    # Create new project if it doesn't exist
                    current_project = ProjectData(
                        id=project_id,
                        name=project_data.get('name', project_id),
                        client_profile=project_data.get('client_profile', {}),
                        project_assumptions=project_data.get('project_assumptions', {}),
                        financial_results=project_data.get('financial_results'),
                        version=0
                    )
                
                # Update project data
                current_project.client_profile = project_data.get('client_profile', current_project.client_profile)
                current_project.project_assumptions = project_data.get('project_assumptions', current_project.project_assumptions)
                current_project.financial_results = project_data.get('financial_results', current_project.financial_results)
                current_project.version += 1
                current_project.modified_at = datetime.now()
                
                # Save with version
                with sqlite3.connect(self.db_path) as conn:
                    # Update or insert project
                    if self.load_project(project_id):
                        conn.execute("""
                            UPDATE projects 
                            SET name = ?, client_profile = ?, project_assumptions = ?, 
                                financial_results = ?, modified_at = ?, version = ?, 
                                tags = ?, description = ?
                            WHERE id = ?
                        """, (
                            current_project.name,
                            json.dumps(current_project.client_profile),
                            json.dumps(current_project.project_assumptions),
                            json.dumps(current_project.financial_results) if current_project.financial_results else None,
                            current_project.modified_at,
                            current_project.version,
                            json.dumps(current_project.tags),
                            current_project.description,
                            current_project.id
                        ))
                    else:
                        conn.execute("""
                            INSERT INTO projects 
                            (id, name, client_profile, project_assumptions, financial_results,
                             created_at, modified_at, version, tags, description)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, (
                            current_project.id,
                            current_project.name,
                            json.dumps(current_project.client_profile),
                            json.dumps(current_project.project_assumptions),
                            json.dumps(current_project.financial_results) if current_project.financial_results else None,
                            current_project.created_at,
                            current_project.modified_at,
                            current_project.version,
                            json.dumps(current_project.tags),
                            current_project.description
                        ))
                    
                    # Save version with comment
                    data = {
                        'project': asdict(current_project),
                        'timestamp': datetime.now().isoformat()
                    }
                    
                    compressed_data = gzip.compress(pickle.dumps(data))
                    checksum = hashlib.sha256(compressed_data).hexdigest()
                    
                    conn.execute("""
                        INSERT OR REPLACE INTO project_versions 
                        (project_id, version, data, checksum, created_at, comment, file_size)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (
                        project_id,
                        current_project.version,
                        compressed_data,
                        checksum,
                        datetime.now(),
                        comment,
                        len(compressed_data)
                    ))
                    
                    # Update recent projects
                    self._update_recent_projects(conn, project_id)
                    
                    conn.commit()
                
                version_id = f"{project_id}_v{current_project.version}"
                self.logger.info(f"Saved project version: {version_id}")
                return version_id
                
        except Exception as e:
            self.logger.error(f"Error saving project version for {project_id}: {e}")
            raise
    
    def load_project(self, project_id: str, version: Optional[int] = None) -> Optional[ProjectData]:
        """Load project by ID, optionally specific version."""
        try:
            with self._lock:
                with sqlite3.connect(self.db_path) as conn:
                    if version:
                        # Load specific version
                        return self._load_project_version(conn, project_id, version)
                    else:
                        # Load latest version
                        row = conn.execute("""
                            SELECT id, name, client_profile, project_assumptions, financial_results,
                                   created_at, modified_at, version, tags, description
                            FROM projects 
                            WHERE id = ? AND is_deleted = FALSE
                        """, (project_id,)).fetchone()
                        
                        if row:
                            # Update recent projects
                            self._update_recent_projects(conn, project_id)
                            conn.commit()
                            
                            return ProjectData(
                                id=row[0],
                                name=row[1],
                                client_profile=json.loads(row[2]),
                                project_assumptions=json.loads(row[3]),
                                financial_results=json.loads(row[4]) if row[4] else None,
                                created_at=datetime.fromisoformat(row[5]),
                                modified_at=datetime.fromisoformat(row[6]),
                                version=row[7],
                                tags=json.loads(row[8]),
                                description=row[9]
                            )
                        
            return None
            
        except Exception as e:
            self.logger.error(f"Error loading project {project_id}: {e}")
            return None
    
    def list_projects(self, limit: int = 50, include_deleted: bool = False) -> List[Dict[str, Any]]:
        """List all projects with metadata."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                where_clause = "" if include_deleted else "WHERE is_deleted = FALSE"
                rows = conn.execute(f"""
                    SELECT id, name, created_at, modified_at, version, tags, description
                    FROM projects 
                    {where_clause}
                    ORDER BY modified_at DESC 
                    LIMIT ?
                """, (limit,)).fetchall()
                
                return [
                    {
                        'id': row[0],
                        'name': row[1],
                        'created_at': row[2],
                        'modified_at': row[3],
                        'version': row[4],
                        'tags': json.loads(row[5]),
                        'description': row[6]
                    }
                    for row in rows
                ]
                
        except Exception as e:
            self.logger.error(f"Error listing projects: {e}")
            return []
    
    def get_recent_projects(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recently accessed projects."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                rows = conn.execute("""
                    SELECT p.id, p.name, p.modified_at, r.last_accessed, r.access_count
                    FROM projects p
                    JOIN recent_projects r ON p.id = r.project_id
                    WHERE p.is_deleted = FALSE
                    ORDER BY r.last_accessed DESC
                    LIMIT ?
                """, (limit,)).fetchall()
                
                return [
                    {
                        'id': row[0],
                        'name': row[1],
                        'modified_at': row[2],
                        'last_accessed': row[3],
                        'access_count': row[4]
                    }
                    for row in rows
                ]
                
        except Exception as e:
            self.logger.error(f"Error getting recent projects: {e}")
            return []
    
    def delete_project(self, project_id: str, soft_delete: bool = True) -> bool:
        """Delete project (soft delete by default)."""
        try:
            with self._lock:
                with sqlite3.connect(self.db_path) as conn:
                    if soft_delete:
                        conn.execute(
                            "UPDATE projects SET is_deleted = TRUE WHERE id = ?", 
                            (project_id,)
                        )
                    else:
                        # Hard delete - remove all versions too
                        conn.execute("DELETE FROM project_versions WHERE project_id = ?", (project_id,))
                        conn.execute("DELETE FROM recent_projects WHERE project_id = ?", (project_id,))
                        conn.execute("DELETE FROM projects WHERE id = ?", (project_id,))
                    
                    conn.commit()
                    
            self.logger.info(f"Project {project_id} {'soft' if soft_delete else 'hard'} deleted")
            return True
            
        except Exception as e:
            self.logger.error(f"Error deleting project {project_id}: {e}")
            return False
    
    def get_project_versions(self, project_id: str) -> List[Dict[str, Any]]:
        """Get all versions of a project."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                rows = conn.execute("""
                    SELECT version, created_at, comment, file_size, checksum
                    FROM project_versions 
                    WHERE project_id = ?
                    ORDER BY version DESC
                """, (project_id,)).fetchall()
                
                return [
                    {
                        'version': row[0],
                        'created_at': row[1],
                        'comment': row[2],
                        'file_size': row[3],
                        'checksum': row[4]
                    }
                    for row in rows
                ]
                
        except Exception as e:
            self.logger.error(f"Error getting versions for project {project_id}: {e}")
            return []
    
    def create_backup(self, backup_name: Optional[str] = None) -> bool:
        """Create database backup."""
        try:
            if not backup_name:
                backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
            
            backup_path = self.backup_dir / backup_name
            shutil.copy2(self.db_path, backup_path)
            
            # Compress backup
            with open(backup_path, 'rb') as f_in:
                with gzip.open(f"{backup_path}.gz", 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            # Remove uncompressed backup
            backup_path.unlink()
            
            self.logger.info(f"Backup created: {backup_path}.gz")
            return True
            
        except Exception as e:
            self.logger.error(f"Error creating backup: {e}")
            return False
    
    def restore_backup(self, backup_path: str) -> bool:
        """Restore database from backup."""
        try:
            backup_file = Path(backup_path)
            
            if backup_file.suffix == '.gz':
                # Decompress backup
                with gzip.open(backup_file, 'rb') as f_in:
                    with open(self.db_path, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
            else:
                shutil.copy2(backup_file, self.db_path)
            
            self.logger.info(f"Database restored from backup: {backup_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error restoring backup: {e}")
            return False
    
    def _save_version(self, conn: sqlite3.Connection, project: ProjectData):
        """Save project version."""
        # Serialize project data
        data = {
            'project': asdict(project),
            'timestamp': datetime.now().isoformat()
        }
        
        # Compress and store
        compressed_data = gzip.compress(pickle.dumps(data))
        checksum = hashlib.sha256(compressed_data).hexdigest()
        
        conn.execute("""
            INSERT OR REPLACE INTO project_versions 
            (project_id, version, data, checksum, created_at, file_size)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            project.id,
            project.version,
            compressed_data,
            checksum,
            datetime.now(),
            len(compressed_data)
        ))
    
    def _load_project_version(self, conn: sqlite3.Connection, project_id: str, version: int) -> Optional[ProjectData]:
        """Load specific project version."""
        row = conn.execute("""
            SELECT data, checksum FROM project_versions 
            WHERE project_id = ? AND version = ?
        """, (project_id, version)).fetchone()
        
        if row:
            # Verify checksum
            compressed_data = row[0]
            expected_checksum = row[1]
            actual_checksum = hashlib.sha256(compressed_data).hexdigest()
            
            if actual_checksum != expected_checksum:
                self.logger.error(f"Checksum mismatch for project {project_id} version {version}")
                return None
            
            # Decompress and deserialize
            data = pickle.loads(gzip.decompress(compressed_data))
            project_dict = data['project']
            
            return ProjectData(**project_dict)
        
        return None
    
    def _update_recent_projects(self, conn: sqlite3.Connection, project_id: str):
        """Update recent projects tracking."""
        conn.execute("""
            INSERT OR REPLACE INTO recent_projects (project_id, last_accessed, access_count)
            VALUES (?, ?, COALESCE((SELECT access_count + 1 FROM recent_projects WHERE project_id = ?), 1))
        """, (project_id, datetime.now(), project_id))
    
    def _setup_auto_backup(self):
        """Setup automatic backup cleanup."""
        try:
            # Clean up old backups (keep last 30 days)
            cutoff_date = datetime.now() - timedelta(days=30)
            
            for backup_file in self.backup_dir.glob("backup_*.db.gz"):
                try:
                    # Extract date from filename
                    date_str = backup_file.stem.split('_')[1] + '_' + backup_file.stem.split('_')[2]
                    backup_date = datetime.strptime(date_str, '%Y%m%d_%H%M%S')
                    
                    if backup_date < cutoff_date:
                        backup_file.unlink()
                        self.logger.info(f"Removed old backup: {backup_file}")
                        
                except (ValueError, IndexError):
                    # Skip files with unexpected naming
                    continue
                    
        except Exception as e:
            self.logger.error(f"Error in backup cleanup: {e}")
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """Get storage statistics."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                stats = {}
                
                # Project counts
                stats['total_projects'] = conn.execute("SELECT COUNT(*) FROM projects WHERE is_deleted = FALSE").fetchone()[0]
                stats['deleted_projects'] = conn.execute("SELECT COUNT(*) FROM projects WHERE is_deleted = TRUE").fetchone()[0]
                
                # Version counts
                stats['total_versions'] = conn.execute("SELECT COUNT(*) FROM project_versions").fetchone()[0]
                
                # Database size
                stats['db_size_bytes'] = self.db_path.stat().st_size
                
                # Backup info
                backup_files = list(self.backup_dir.glob("*.gz"))
                stats['backup_count'] = len(backup_files)
                stats['backup_size_bytes'] = sum(f.stat().st_size for f in backup_files)
                
                return stats
                
        except Exception as e:
            self.logger.error(f"Error getting storage stats: {e}")
            return {}
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get persistence service statistics."""
        try:
            stats = {
                'database_path': str(self.db_path),
                'database_exists': self.db_path.exists(),
                'backup_directory': str(self.backup_dir),
                'total_projects': 0,
                'total_versions': 0,
                'recent_projects_count': 0,
                'database_size_mb': 0
            }
            
            if self.db_path.exists():
                # Get database size
                stats['database_size_mb'] = round(self.db_path.stat().st_size / (1024 * 1024), 2)
                
                # Get counts from database
                with sqlite3.connect(str(self.db_path)) as conn:
                    cursor = conn.cursor()
                    
                    # Count projects
                    cursor.execute("SELECT COUNT(*) FROM projects WHERE is_deleted = FALSE")
                    stats['total_projects'] = cursor.fetchone()[0]
                    
                    # Count versions
                    cursor.execute("SELECT COUNT(*) FROM project_versions")
                    stats['total_versions'] = cursor.fetchone()[0]
                    
                    # Count recent projects
                    cursor.execute("SELECT COUNT(*) FROM recent_projects")
                    stats['recent_projects_count'] = cursor.fetchone()[0]
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Failed to get statistics: {e}")
            return {
                'error': str(e),
                'database_path': str(self.db_path),
                'database_exists': False
            }
