#!/usr/bin/env python3
"""
Test UI Integration with Enhanced Features
==========================================

Test script to verify UI integration and check when ML & 3D charts are triggered.
"""

import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from services.enhanced_integration_service import get_integration_service
from app.app_controller import AppController
from models.client_profile import ClientProfile
from models.project_assumptions import EnhancedProjectAssumptions

def test_ui_integration():
    """Test UI integration with enhanced features."""
    print("🔗 UI Integration Test with Enhanced Features")
    print("=" * 60)
    
    # Test 1: Enhanced Integration Service availability
    print("\n📊 Testing Enhanced Integration Service...")
    integration_service = get_integration_service()
    print(f"✓ Integration service initialized")
    print(f"✓ Features available: {list(integration_service.features.keys())}")
    print(f"✓ Features enabled: {[k for k, v in integration_service.features.items() if v]}")
    
    # Test 2: Data persistence check
    print("\n💾 Testing Data Persistence...")
    if integration_service.persistence_service:
        projects = integration_service.persistence_service.list_projects()
        print(f"✓ Can access database - {len(projects)} projects found")
        
        # Test save functionality
        from services.persistence_service import ProjectData
        test_project = ProjectData(
            id="test_integration",
            name="UI Integration Test",
            client_profile={"company": "Test Company"},
            project_assumptions={"capacity_mw": 10.0}
        )
        
        save_success = integration_service.persistence_service.save_project(test_project)
        print(f"✓ Save test: {'Success' if save_success else 'Failed'}")
        
        # Cleanup test project
        integration_service.persistence_service.delete_project("test_integration", soft_delete=False)
    else:
        print("⚠ Persistence service not available")
    
    # Test 3: Check comprehensive analysis workflow
    print("\n🚀 Testing Comprehensive Analysis Workflow...")
    
    # Create test data
    test_client_profile = ClientProfile(
        company_name="Test Company",
        project_name="UI Test Project", 
        client_name="Test User",
        project_location="Ouarzazate"
    )
    
    test_assumptions = EnhancedProjectAssumptions(
        capacity_mw=25.0,
        capex_meur=27.5,  # 25 MW * 1.1 MEUR/MW
        production_mwh_year1=61320,  # 25 MW * 0.28 CF * 8760 hours
        ppa_price_eur_kwh=0.045
    )
    
    # Test enhanced financial model
    project_data = {
        'assumptions': test_assumptions.to_dict()
    }
    
    print("  Running enhanced financial model...")
    results = integration_service.run_enhanced_financial_model(
        project_data,
        include_ml_predictions=True,
        include_monte_carlo=True
    )
    
    print(f"✓ Enhanced model completed")
    print(f"  Base IRR: {results.get('kpis', {}).get('irr_equity', 0):.1%}")
    
    # Check ML predictions
    ml_results = results.get('ml_predictions', {})
    if ml_results and 'predictions' in ml_results:
        print(f"✓ ML predictions generated: {len(ml_results['predictions'])} targets")
        for target, prediction in ml_results['predictions'].items():
            predicted_value = prediction.get('predicted_value', 0)
            print(f"    {target}: {predicted_value:.3f}")
    else:
        print("⚠ No ML predictions in results")
    
    # Check Monte Carlo
    mc_results = results.get('monte_carlo', {})
    if mc_results:
        iterations = mc_results.get('iterations', 0)
        print(f"✓ Monte Carlo simulation: {iterations} iterations")
    else:
        print("⚠ No Monte Carlo results")
    
    # Test 4: Check 3D chart generation
    print("\n📈 Testing 3D Chart Generation...")
    if integration_service.charts_3d_service:
        try:
            charts = integration_service.generate_advanced_charts(
                financial_results=results,
                project_name="UI Test Project"
            )
            
            chart_types = list(charts.keys())
            print(f"✓ Generated {len(chart_types)} chart types: {chart_types}")
            
            # Check for 3D charts specifically
            chart_3d_types = [ct for ct in chart_types if '3d' in ct.lower()]
            if chart_3d_types:
                print(f"✓ 3D charts generated: {chart_3d_types}")
            else:
                print("⚠ No 3D charts found in output")
                
        except Exception as e:
            print(f"⚠ Chart generation failed: {e}")
    else:
        print("⚠ 3D chart service not available")
    
    # Test 5: Location comparison with defaults
    print("\n🌍 Testing Location Comparison...")
    print(f"Default location: {test_client_profile.project_location}")
    
    # Test location manager
    from models.location_config import LocationManager
    location_manager = LocationManager()
    
    available_locations = location_manager.get_location_names()
    print(f"✓ Available locations: {len(available_locations)}")
    print(f"  Morocco locations: {[loc for loc in available_locations if any(x in loc.lower() for x in ['ouarzazate', 'dakhla', 'laayoune'])]}")
    
    # Check default settings
    ouarzazate_config = location_manager.get_location("Ouarzazate")
    dakhla_config = location_manager.get_location("Dakhla")
    
    if ouarzazate_config:
        print(f"✓ Ouarzazate config: {ouarzazate_config.production_mwh_year1} MWh/year")
    if dakhla_config:
        print(f"✓ Dakhla config: {dakhla_config.production_mwh_year1} MWh/year")
    
    # Test 6: UI workflow simulation
    print("\n🖥️ Testing UI Workflow...")
    
    # Simulate "Generate Complete Analysis & Reports" button click
    print("  Simulating comprehensive analysis button click...")
    
    # This would normally be triggered by the UI button
    comprehensive_params = {
        'client_profile': test_client_profile.to_dict(),
        'assumptions': test_assumptions.to_dict(),
        'include_location_comparison': True,
        'include_ml_predictions': True,
        'include_3d_charts': True
    }
    
    print(f"✓ Button click would trigger:")
    print(f"    Location comparison: {comprehensive_params['include_location_comparison']}")
    print(f"    ML predictions: {comprehensive_params['include_ml_predictions']}")
    print(f"    3D charts: {comprehensive_params['include_3d_charts']}")
    
    # Summary
    print("\n📋 Integration Summary:")
    print("=" * 40)
    
    integration_score = 0
    total_tests = 6
    
    if integration_service.features['persistence']:
        integration_score += 1
        print("✓ Data persistence integrated")
    
    if integration_service.features['caching']:
        integration_score += 1
        print("✓ Performance caching integrated")
    
    if integration_service.features['ml']:
        integration_score += 1
        print("✓ ML predictions integrated")
    
    if integration_service.features['3d_charts']:
        integration_score += 1
        print("✓ 3D charts integrated")
    
    if integration_service.features['undo_redo']:
        integration_score += 1
        print("✓ Undo/redo integrated")
        
    if integration_service.features['recovery']:
        integration_score += 1
        print("✓ Error recovery integrated")
    
    print(f"\n🎯 Integration Score: {integration_score}/{total_tests} ({integration_score/total_tests:.1%})")
    
    if integration_score >= 5:
        print("🎉 Excellent integration! All major features working.")
    elif integration_score >= 3:
        print("👍 Good integration! Most features working.")
    else:
        print("⚠️ Integration issues detected. Some features may not work.")
    
    return integration_score == total_tests

if __name__ == "__main__":
    success = test_ui_integration()
    sys.exit(0 if success else 1) 