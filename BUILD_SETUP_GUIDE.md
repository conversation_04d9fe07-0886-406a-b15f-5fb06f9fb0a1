# 🏗️ Build Setup Guide - Hiel Renewable Energy Financial Modeler

## 📋 Overview

This guide will help you build a standalone executable (`.exe`) for the **Hiel Renewable Energy Financial Modeler** application. The final executable will be named `HielRnEModeler.exe` and can run on any Windows machine without requiring Python installation.

## 🎯 Application Details

- **Name**: Hiel Renewable Energy Financial Modeler
- **Short Name**: HielRnEModeler
- **Version**: 3.0.0
- **Description**: Professional Financial Modeling Tool for Renewable Energy Projects
- **Output**: Single executable file (~50-150 MB)

## 📋 Prerequisites

### 1. Python Environment
- **Python 3.8 or higher** (recommended: 3.9+)
- All dependencies from `requirements.txt` installed

### 2. Required Packages
The build script will check these automatically:
```bash
pip install flet
pip install numpy pandas numpy_financial
pip install pyinstaller  # For fallback option
```

### 3. Logo Setup (Optional but Recommended)
1. Create an `assets` folder in the project root
2. Place your logo as `assets/logo.ico` (ICO format, 256x256 recommended)
3. If no logo is provided, the executable will use the default icon

## 🚀 Quick Start

### Option 1: Use the Batch File (Easiest)
1. Double-click `build.bat`
2. Choose option 1 (Standard build)
3. Wait for completion
4. Find your executable in the `dist` folder

### Option 2: Command Line
```bash
# Standard build
python build_exe.py

# Debug build (with verbose output)
python build_exe.py --debug

# Clean build (removes previous builds)
python build_exe.py --clean

# Force PyInstaller (if Flet build fails)
python build_exe.py --pyinstaller
```

## 📁 Logo Setup Instructions

### Converting Your Logo to ICO Format

1. **Using Online Converter** (Recommended):
   - Go to https://convertio.co/png-ico/ or similar
   - Upload your logo (PNG/JPG preferred)
   - Set size to 256x256 pixels
   - Download as `.ico` file
   - Save as `assets/logo.ico`

2. **Using GIMP** (Free):
   - Open your logo in GIMP
   - Image → Scale Image → 256x256 pixels
   - File → Export As → `logo.ico`
   - Choose all sizes when prompted

3. **Using ImageMagick**:
   ```bash
   magick convert logo.png -resize 256x256 assets/logo.ico
   ```

### Logo Requirements
- Format: `.ico` (Windows Icon)
- Recommended size: 256x256 pixels
- Location: `assets/logo.ico`
- The logo will appear in the executable and system tray

## 🔧 Build Process Details

### What the Build Script Does

1. **Dependency Check**: Verifies all required packages are installed
2. **Asset Setup**: Creates assets folder and checks for logo
3. **Primary Build**: Uses Flet's built-in build system (recommended)
4. **Fallback Build**: Uses PyInstaller if Flet build fails
5. **Post-Processing**: Creates version info and build metadata

### Build Outputs

After successful build, you'll find:
```
dist/
├── HielRnEModeler.exe     # Main executable (~50-150 MB)
└── build_info.json       # Build metadata

build/                     # Temporary build files (can be deleted)
version_info.txt          # Windows version information
```

## 📊 Expected Results

### Successful Build Output
```
✅ Build completed successfully!

📦 Executable location: dist/HielRnEModeler.exe
📊 Application: Hiel Renewable Energy Financial Modeler v3.0.0
💼 Description: Professional Financial Modeling Tool for Renewable Energy Projects
📏 File size: ~85.2 MB

🚀 You can now distribute the executable file!
```

### File Characteristics
- **Size**: Typically 50-150 MB (includes Python runtime and all dependencies)
- **Startup**: 3-5 seconds on typical hardware
- **Dependencies**: None required on target machine
- **Compatibility**: Windows 7+ (64-bit recommended)

## 🛠️ Troubleshooting

### Common Issues

#### "Flet not installed" Error
```bash
pip install flet
# or
pip install -r requirements.txt
```

#### "PyInstaller not installed" Error
```bash
pip install pyinstaller
```

#### Large Executable Size
- Normal for Python apps with scientific libraries
- Use `--debug` flag to see what's being included
- Consider excluding unused modules (advanced)

#### Import Errors During Runtime
- The build script includes common hidden imports
- If specific modules fail, add them to the `hidden_imports` list in `build_exe.py`

#### Logo Not Appearing
- Ensure `assets/logo.ico` exists and is valid
- Use online ICO validator tools
- Check file permissions

### Advanced Troubleshooting

#### Enable Debug Mode
```bash
python build_exe.py --debug
```

#### Force PyInstaller Build
```bash
python build_exe.py --pyinstaller --debug
```

#### Clean Build Environment
```bash
python build_exe.py --clean
```

#### Manual Dependency Check
```python
import flet
import numpy
import pandas
import numpy_financial
print("All dependencies available!")
```

## 🎨 Customization Options

### Modify Application Details
Edit these variables in `build_exe.py`:
```python
APP_NAME = "Your Custom Name"
APP_SHORT_NAME = "YourAppName"
APP_VERSION = "1.0.0"
APP_DESCRIPTION = "Your Description"
APP_COPYRIGHT = "© 2025 Your Company"
```

### Add Additional Data Files
Modify the `data_files` list in `build_exe.py`:
```python
data_files = [
    ("config", "config"),
    ("templates", "templates"),
    ("your_folder", "your_folder"),  # Add this line
]
```

### Exclude Unused Modules
Add to PyInstaller command in `build_exe.py`:
```python
cmd.extend(["--exclude-module", "unused_module"])
```

## 📦 Distribution

### What to Distribute
- **Required**: `HielRnEModeler.exe`
- **Optional**: `build_info.json` (for support purposes)
- **Not needed**: Everything else in the project folder

### Distribution Checklist
- [ ] Test executable on clean Windows machine
- [ ] Verify all features work correctly
- [ ] Check file associations and icon display
- [ ] Test with Windows Defender (may flag first run)
- [ ] Include brief user instructions

### Installation Instructions for End Users
1. Download `HielRnEModeler.exe`
2. Place in desired folder (e.g., `C:\Programs\HielRnE\`)
3. Right-click executable → "Run as administrator" (first time only)
4. Create desktop shortcut if desired
5. Windows may show security warning - click "More info" → "Run anyway"

## 🔒 Security Notes

- The executable is not code-signed, so Windows may show security warnings
- Consider code signing for professional distribution
- Antivirus software may flag the executable (false positive common with PyInstaller)
- Test on multiple Windows versions before wide distribution

## 📞 Support

### Before Building
- Ensure Python environment is clean
- Update all dependencies to latest versions
- Test the application normally before building

### Build Issues
- Check Python version compatibility
- Verify all imports work in development
- Use debug mode for detailed error messages

### Runtime Issues
- Test executable on target Windows versions
- Check for missing Visual C++ redistributables
- Verify file permissions in installation folder

---

**🎉 Happy Building!** Your professional renewable energy financial modeling tool is ready for distribution. 