#!/usr/bin/env python3
"""
Test script to verify that the dashboard update fix works correctly.
This tests that the enhanced comprehensive analysis properly updates the dashboard.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.client_profile import ClientProfile
from models.project_assumptions import EnhancedProjectAssumptions
from services.enhanced_integration_service import get_integration_service

def test_dashboard_update_fix():
    """Test that enhanced service returns proper financial results for dashboard."""
    
    print("🧪 Testing Dashboard Update Fix...")
    print("=" * 60)
    
    # Create test data
    print("\n1. Creating test client profile and assumptions...")
    
    client_profile = ClientProfile(
        company_name="Test Solar Company",
        project_name="Test Solar Project",
        contact_email="<EMAIL>"
    )
    
    assumptions = EnhancedProjectAssumptions(
        capacity_mw=10.0,
        capex_meur=12.0,  # 10 MW * 1.2 MEUR/MW
        production_mwh_year1=21900.0,  # 10 MW * 25% CF * 8760 hours
        ppa_price_eur_kwh=0.05,
        debt_ratio=0.75,
        interest_rate=0.06,
        discount_rate=0.08,
        project_life_years=25
    )
    
    print(f"✓ Client: {client_profile.company_name}")
    print(f"✓ Project: {assumptions.capacity_mw} MW solar project")
    
    # Test enhanced service
    print("\n2. Testing Enhanced Integration Service...")
    
    try:
        integration_service = get_integration_service()
        print(f"✓ Integration service initialized")
        
        # Prepare project data
        project_data = {
            'client_profile': client_profile.to_dict(),
            'assumptions': assumptions.to_dict()
        }
        
        print("\n3. Running enhanced financial model...")
        
        # Run enhanced financial model (this should now call the real financial service)
        enhanced_results = integration_service.run_enhanced_financial_model(
            project_data=project_data,
            include_ml_predictions=True,
            include_monte_carlo=True
        )
        
        print("✓ Enhanced financial model completed")
        
        # Check results structure
        print("\n4. Analyzing results structure...")
        
        print(f"✓ Results keys: {list(enhanced_results.keys())}")
        
        if 'kpis' in enhanced_results:
            kpis = enhanced_results['kpis']
            print(f"✓ KPIs found: {list(kpis.keys())}")
            
            # Check for key financial metrics
            key_metrics = ['IRR_equity', 'NPV_equity', 'LCOE_eur_kwh', 'IRR_project', 'NPV_project']
            found_metrics = [metric for metric in key_metrics if metric in kpis]
            print(f"✓ Key metrics found: {found_metrics}")
            
            if found_metrics:
                print("✅ SUCCESS: Enhanced service returns proper financial results!")
                print("\n📊 Sample KPIs:")
                for metric in found_metrics[:3]:  # Show first 3 metrics
                    value = kpis[metric]
                    if 'IRR' in metric:
                        print(f"  {metric}: {value:.1%}")
                    elif 'NPV' in metric:
                        print(f"  {metric}: €{value:,.0f}")
                    elif 'LCOE' in metric:
                        print(f"  {metric}: €{value:.3f}/kWh")
            else:
                print("❌ ISSUE: No key financial metrics found in results")
        else:
            print("❌ ISSUE: No 'kpis' section found in results")
        
        # Check for enhanced features
        print("\n5. Checking enhanced features...")
        
        enhanced_features = []
        if 'ml_predictions' in enhanced_results and enhanced_results['ml_predictions']:
            enhanced_features.append("ML Predictions")
        if 'monte_carlo' in enhanced_results and enhanced_results['monte_carlo']:
            enhanced_features.append("Monte Carlo")
        if 'enhanced_kpis' in enhanced_results and enhanced_results['enhanced_kpis']:
            enhanced_features.append("Enhanced KPIs")
        
        if enhanced_features:
            print(f"✓ Enhanced features active: {', '.join(enhanced_features)}")
        else:
            print("⚠️  No enhanced features detected")
        
        # Check if fallback was used
        if enhanced_results.get('fallback_used'):
            print(f"⚠️  Fallback was used: {enhanced_results.get('fallback_reason')}")
        else:
            print("✓ Real financial model was used (no fallback)")
        
        print("\n" + "=" * 60)
        print("🎯 CONCLUSION:")
        
        if 'kpis' in enhanced_results and any(metric in enhanced_results['kpis'] for metric in key_metrics):
            print("✅ Dashboard update fix is WORKING!")
            print("   Enhanced service now returns proper financial results")
            print("   that the dashboard can display correctly.")
        else:
            print("❌ Dashboard update fix needs more work.")
            print("   Enhanced service is not returning expected financial structure.")
        
        return enhanced_results
        
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_dashboard_update_fix()
