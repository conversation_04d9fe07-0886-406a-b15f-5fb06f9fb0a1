#!/usr/bin/env python3
"""
Test script to verify IRR fix works with the actual financial service.
"""

import sys
import os
import logging

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(level=logging.INFO)

try:
    from services.financial_service import FinancialModelService
    from models.project_assumptions import EnhancedProjectAssumptions
    print("✅ Successfully imported financial service")
except ImportError as e:
    print(f"❌ Failed to import financial service: {e}")
    sys.exit(1)

def test_financial_service_irr():
    """Test IRR calculation through the financial service."""
    print("\n🔍 Testing Financial Service IRR Calculation")
    print("=" * 50)
    
    # Create financial service
    service = FinancialModelService()
    
    # Test scenarios
    scenarios = [
        {
            "name": "Standard Solar Project",
            "assumptions": EnhancedProjectAssumptions(
                capacity_mw=10.0,
                capex_meur=8.5,
                production_mwh_year1=18000.0,
                ppa_price_eur_kwh=0.045,
                debt_ratio=0.75,
                interest_rate=0.06,
                project_life_years=25,
                opex_keuros_year1=180.0
            )
        },
        {
            "name": "Large Scale Project",
            "assumptions": EnhancedProjectAssumptions(
                capacity_mw=50.0,
                capex_meur=42.5,
                production_mwh_year1=90000.0,
                ppa_price_eur_kwh=0.042,
                debt_ratio=0.80,
                interest_rate=0.055,
                project_life_years=25,
                opex_keuros_year1=900.0
            )
        },
        {
            "name": "High Grant Project",
            "assumptions": EnhancedProjectAssumptions(
                capacity_mw=10.0,
                capex_meur=8.5,
                production_mwh_year1=18000.0,
                ppa_price_eur_kwh=0.045,
                debt_ratio=0.75,
                interest_rate=0.06,
                project_life_years=25,
                opex_keuros_year1=180.0,
                total_grants_meur=2.0  # High grants
            )
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{i}. Testing {scenario['name']}")
        print("-" * 40)
        
        try:
            # Run financial model
            results = service.run_financial_model(scenario['assumptions'])
            
            if results and 'kpis' in results:
                kpis = results['kpis']
                
                irr_equity = kpis.get('IRR_equity', 'N/A')
                irr_project = kpis.get('IRR_project', 'N/A')
                npv_equity = kpis.get('NPV_equity', 'N/A')
                npv_project = kpis.get('NPV_project', 'N/A')
                lcoe = kpis.get('LCOE_eur_kwh', 'N/A')
                
                print(f"   ✅ Model executed successfully")
                
                if isinstance(irr_equity, (int, float)) and not (irr_equity != irr_equity):  # Check for NaN
                    print(f"   ✅ IRR Equity: {irr_equity:.2%}")
                else:
                    print(f"   ❌ IRR Equity: FAILED ({irr_equity})")
                    
                if isinstance(irr_project, (int, float)) and not (irr_project != irr_project):  # Check for NaN
                    print(f"   ✅ IRR Project: {irr_project:.2%}")
                else:
                    print(f"   ❌ IRR Project: FAILED ({irr_project})")
                    
                if isinstance(npv_equity, (int, float)) and not (npv_equity != npv_equity):
                    print(f"   📊 NPV Equity: €{npv_equity:,.0f}")
                    
                if isinstance(npv_project, (int, float)) and not (npv_project != npv_project):
                    print(f"   📊 NPV Project: €{npv_project:,.0f}")
                    
                if isinstance(lcoe, (int, float)) and not (lcoe != lcoe):
                    print(f"   📊 LCOE: €{lcoe:.3f}/kWh")
                    
                # Check if fallback was used
                if results.get('is_fallback', False):
                    print(f"   ⚠️  WARNING: Fallback results used")
                    print(f"   📋 Reason: {results.get('fallback_reason', 'Unknown')}")
                    
            else:
                print(f"   ❌ Model failed to return results")
                
        except Exception as e:
            print(f"   ❌ ERROR: {str(e)}")
            import traceback
            print(f"   📋 Traceback: {traceback.format_exc()}")

def test_edge_cases():
    """Test edge cases that might cause IRR failures."""
    print("\n🔍 Testing Edge Cases")
    print("=" * 30)
    
    service = FinancialModelService()
    
    edge_cases = [
        {
            "name": "Very Low Revenue",
            "assumptions": EnhancedProjectAssumptions(
                capacity_mw=1.0,
                capex_meur=2.0,
                production_mwh_year1=1000.0,
                ppa_price_eur_kwh=0.02,  # Very low price
                debt_ratio=0.75,
                interest_rate=0.06,
                project_life_years=25,
                opex_keuros_year1=50.0
            )
        },
        {
            "name": "No Debt Project",
            "assumptions": EnhancedProjectAssumptions(
                capacity_mw=10.0,
                capex_meur=8.5,
                production_mwh_year1=18000.0,
                ppa_price_eur_kwh=0.045,
                debt_ratio=0.0,  # No debt
                interest_rate=0.06,
                project_life_years=25,
                opex_keuros_year1=180.0
            )
        },
        {
            "name": "High Grants (90%)",
            "assumptions": EnhancedProjectAssumptions(
                capacity_mw=10.0,
                capex_meur=8.5,
                production_mwh_year1=18000.0,
                ppa_price_eur_kwh=0.045,
                debt_ratio=0.75,
                interest_rate=0.06,
                project_life_years=25,
                opex_keuros_year1=180.0,
                total_grants_meur=7.65  # 90% grants
            )
        }
    ]
    
    for i, case in enumerate(edge_cases, 1):
        print(f"\n{i}. {case['name']}")
        print("-" * 25)
        
        try:
            results = service.run_financial_model(case['assumptions'])
            
            if results and 'kpis' in results:
                kpis = results['kpis']
                irr_equity = kpis.get('IRR_equity', 'N/A')
                irr_project = kpis.get('IRR_project', 'N/A')
                
                if isinstance(irr_equity, (int, float)) and not (irr_equity != irr_equity):
                    print(f"   ✅ IRR Equity: {irr_equity:.2%}")
                else:
                    print(f"   ❌ IRR Equity: FAILED")
                    
                if isinstance(irr_project, (int, float)) and not (irr_project != irr_project):
                    print(f"   ✅ IRR Project: {irr_project:.2%}")
                else:
                    print(f"   ❌ IRR Project: FAILED")
            else:
                print(f"   ❌ Model execution failed")
                
        except Exception as e:
            print(f"   ❌ ERROR: {str(e)}")

if __name__ == "__main__":
    print("🚀 Starting IRR Fix Verification")
    print("=" * 60)
    
    # Test financial service
    test_financial_service_irr()
    
    # Test edge cases
    test_edge_cases()
    
    print("\n✅ IRR Fix Verification Complete")
    print("\n📋 Summary:")
    print("   - Fixed Project IRR calculation by including initial CAPEX")
    print("   - Improved Equity IRR calculation logic")
    print("   - Enhanced error handling and validation")
    print("   - Added fallback IRR implementation improvements")
