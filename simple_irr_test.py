#!/usr/bin/env python3
"""Simple test to verify IRR fix."""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.enhanced_dcf_model import EnhancedDCFModel, EnhancedDCFAssumptions

def test_irr():
    print("🧪 Testing IRR Fix")
    print("=" * 30)
    
    model = EnhancedDCFModel()
    assumptions = EnhancedDCFAssumptions()
    
    print(f"📊 Test assumptions:")
    print(f"   Capacity: {assumptions.capacity_mw} MW")
    print(f"   CAPEX: €{assumptions.capex_meur} M")
    print(f"   PPA Price: €{assumptions.ppa_price_eur_kwh}/kWh")
    print(f"   Debt Ratio: {assumptions.debt_ratio:.0%}")
    
    # Build cashflow
    cashflow = model.build_cashflow(assumptions)
    print(f"\n✅ Cashflow built: {len(cashflow)} years")
    
    # Compute KPIs
    kpis = model.compute_kpis(cashflow, assumptions)
    
    irr_equity = kpis.get('IRR_equity', 'N/A')
    irr_project = kpis.get('IRR_project', 'N/A')
    
    print(f"\n📈 Results:")
    if isinstance(irr_equity, (int, float)) and not (irr_equity != irr_equity):
        print(f"   ✅ IRR Equity: {irr_equity:.2%}")
    else:
        print(f"   ❌ IRR Equity: FAILED ({irr_equity})")
        
    if isinstance(irr_project, (int, float)) and not (irr_project != irr_project):
        print(f"   ✅ IRR Project: {irr_project:.2%}")
    else:
        print(f"   ❌ IRR Project: FAILED ({irr_project})")
    
    # Show cash flows
    equity_cf = cashflow["Equity_CF"].values
    firm_cf = cashflow["Total_FCF_Firm"].values
    
    print(f"\n🔍 Cash Flow Analysis:")
    print(f"   Equity CF[0]: €{equity_cf[0]:,.0f}")
    print(f"   Firm CF[0]: €{firm_cf[0]:,.0f}")
    print(f"   Equity CF sum (positive): €{equity_cf[equity_cf > 0].sum():,.0f}")
    print(f"   Firm CF sum (positive): €{firm_cf[firm_cf > 0].sum():,.0f}")

if __name__ == "__main__":
    test_irr()
