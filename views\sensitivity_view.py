"""
Sensitivity Analysis View
=========================

View component for sensitivity analysis results.
"""

import flet as ft
from typing import Dict, Any, Optional
import pandas as pd

from .base_view import BaseView


class SensitivityView(BaseView):
    """View for sensitivity analysis results."""
    
    def __init__(self, page: ft.Page):
        super().__init__(page)
        self.sensitivity_results: Optional[pd.DataFrame] = None
        self.selected_variables = ['production_mwh_year1', 'ppa_price_eur_kwh', 'capex_meur', 'discount_rate']
    
    def build_content(self) -> ft.Control:
        """Build the sensitivity analysis view content."""
        
        # Header
        header = self.create_section_header(
            "Sensitivity Analysis",
            "Analyze the impact of key variables on project returns"
        )
        
        # Variable selection
        variable_selection = self._create_variable_selection()
        
        # Results display
        if self.sensitivity_results is not None:
            results_content = self._create_results_display()
        else:
            results_content = self.create_empty_state(
                "No Sensitivity Results",
                "Run sensitivity analysis to see results",
                "Run Analysis",
                self._on_run_analysis
            )
        
        return ft.Column([
            header,
            variable_selection,
            results_content
        ], expand=True, scroll=ft.ScrollMode.AUTO)
    
    def _create_variable_selection(self) -> ft.Card:
        """Create variable selection interface."""
        available_variables = [
            'production_mwh_year1', 'ppa_price_eur_kwh', 'capex_meur', 
            'opex_keuros_year1', 'discount_rate', 'debt_ratio', 'interest_rate'
        ]
        
        variable_checkboxes = []
        for var in available_variables:
            checkbox = ft.Checkbox(
                label=var.replace('_', ' ').title(),
                value=var in self.selected_variables,
                on_change=lambda e, v=var: self._on_variable_selected(v, e.control.value)
            )
            variable_checkboxes.append(checkbox)
        
        selection_content = ft.Column([
            ft.Text("Select Variables for Analysis:", size=16, weight=ft.FontWeight.BOLD),
            ft.Column(variable_checkboxes),
            ft.Divider(height=20),
            self.create_action_button(
                "Run Sensitivity Analysis",
                ft.Icons.ANALYTICS,
                self._on_run_analysis,
                ft.Colors.GREEN_600
            )
        ])
        
        return self.create_card(
            "Variable Selection",
            selection_content,
            icon=ft.Icons.TUNE
        )
    
    def _create_results_display(self) -> ft.Card:
        """Create sensitivity results display."""
        # Placeholder for sensitivity results
        placeholder = ft.Container(
            content=ft.Text("Sensitivity analysis results and charts would be displayed here",
                          text_align=ft.TextAlign.CENTER),
            height=400,
            alignment=ft.alignment.center,
            bgcolor=ft.Colors.GREY_100,
            border_radius=8
        )
        
        return self.create_card(
            "Sensitivity Analysis Results",
            placeholder,
            icon=ft.Icons.SHOW_CHART
        )
    
    def _on_variable_selected(self, variable: str, selected: bool):
        """Handle variable selection change."""
        if selected and variable not in self.selected_variables:
            self.selected_variables.append(variable)
        elif not selected and variable in self.selected_variables:
            self.selected_variables.remove(variable)
    
    def _on_run_analysis(self, e=None):
        """Run sensitivity analysis."""
        if len(self.selected_variables) < 1:
            self.show_error("Please select at least one variable for analysis")
            return
        
        self.request_action("run_sensitivity_analysis", {
            "variables": self.selected_variables
        })
    
    def update_data(self, data: Dict[str, Any]):
        """Update view with sensitivity results."""
        if "sensitivity_results" in data:
            self.sensitivity_results = data["sensitivity_results"]
            self.refresh()
    
    def set_sensitivity_results(self, results: pd.DataFrame):
        """Set sensitivity results."""
        self.sensitivity_results = results
        self.refresh()
