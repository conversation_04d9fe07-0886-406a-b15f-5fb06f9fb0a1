"""
Application Controller
======================

Main application controller coordinating between views and services.
"""

import flet as ft
from typing import Dict, Any, Optional, Callable
import logging
import asyncio
from pathlib import Path

from app.app_state import AppState
from models.ui_state import UIState, TabState
from models.client_profile import ClientProfile
from models.project_assumptions import EnhancedProjectAssumptions

from services.financial_service import FinancialModelService
from services.validation_service import ValidationService
from services.export_service import ExportService
from services.location_service import LocationComparisonService
from services.report_service import ReportGenerationService
from services.health_monitor import setup_health_monitoring, get_health_status
from services.error_handler import global_error_handler

# Enhanced Integration Service
from services.enhanced_integration_service import get_integration_service

from views.project_setup_view import ProjectSetupView
from views.dashboard_view import DashboardView
from views.location_comparison_view import LocationComparisonView
from views.financial_model_view import FinancialModelView
from views.validation_view import ValidationView
from views.sensitivity_view import SensitivityView
from views.monte_carlo_view import <PERSON><PERSON><PERSON><PERSON>View
from views.scenarios_view import ScenariosView
from views.export_view import ExportView

from utils.file_utils import FileUtils


class AppController:
    """Main application controller."""
    
    def __init__(self, page: ft.Page):
        self.page = page
        self.logger = logging.getLogger(__name__)
        
        # Initialize state
        self.app_state = AppState()
        self.ui_state = UIState()
        
        # Initialize services with error handling
        try:
            # Enhanced Integration Service (primary)
            self.enhanced_service = get_integration_service()
            
            # Standard services (for compatibility)
            self.financial_service = FinancialModelService()
            self.validation_service = ValidationService()
            self.export_service = ExportService()
            self.location_service = LocationComparisonService()
            self.report_service = ReportGenerationService()
            self.file_utils = FileUtils()

            # Setup health monitoring
            setup_health_monitoring()
            self.logger.info("All services initialized successfully with enhanced features and health monitoring")

        except Exception as e:
            global_error_handler.handle_error(e,
                context={'function': 'service_initialization'},
                show_user_message=True,
                page=self.page)
            self.logger.error(f"Failed to initialize services: {str(e)}")
        
        # Initialize views
        self.views = {}
        self._initialize_views()
        
        # UI components
        self.status_bar: Optional[ft.Container] = None
        self.progress_bar: Optional[ft.ProgressBar] = None
        self.main_content: Optional[ft.Container] = None
        self.navigation_rail: Optional[ft.NavigationRail] = None
        
        # Setup page
        self._setup_page()
    
    def _initialize_views(self):
        """Initialize all view components."""
        self.views = {
            TabState.PROJECT_SETUP: ProjectSetupView(self.page),
            TabState.DASHBOARD: DashboardView(self.page),
            TabState.LOCATION_COMPARISON: LocationComparisonView(self.page),
            TabState.FINANCIAL_MODEL: FinancialModelView(self.page),
            TabState.VALIDATION: ValidationView(self.page),
            TabState.SENSITIVITY: SensitivityView(self.page),
            TabState.MONTE_CARLO: MonteCarloView(self.page),
            TabState.SCENARIOS: ScenariosView(self.page),
            TabState.EXPORT: ExportView(self.page)
        }
        
        # Setup view callbacks
        for view in self.views.values():
            view.on_navigate = self.navigate_to_tab
            view.on_data_changed = self.handle_data_change
            view.on_action_requested = self.handle_action_request
            view.on_status_update = self.update_status
    
    def _setup_page(self):
        """Setup the main page layout."""
        self.page.title = "Enhanced Financial Model - Agevolami SRL"
        self.page.theme_mode = ft.ThemeMode.LIGHT
        self.page.padding = 0
        
        # Setup keyboard shortcuts for enhanced features
        self.page.on_keyboard_event = self._on_keyboard_event
        
        # Create main layout
        self._create_navigation_rail()
        self._create_main_content()
        self._create_status_bar()
        
        # Setup page layout
        self.page.add(
            ft.Row([
                self.navigation_rail,
                ft.VerticalDivider(width=1),
                ft.Column([
                    self.main_content,
                    self.status_bar
                ], expand=True)
            ], expand=True)
        )
        
        # Initial navigation
        self.navigate_to_tab(TabState.PROJECT_SETUP)
    
    def _create_navigation_rail(self):
        """Create modern navigation rail with enhanced visual feedback."""
        destinations = [
            ft.NavigationRailDestination(
                icon=ft.Icons.SETTINGS_OUTLINED,
                selected_icon=ft.Icons.SETTINGS,
                label="Setup",
                padding=ft.padding.symmetric(vertical=8)
            ),
            ft.NavigationRailDestination(
                icon=ft.Icons.DASHBOARD_OUTLINED,
                selected_icon=ft.Icons.DASHBOARD,
                label="Dashboard",
                padding=ft.padding.symmetric(vertical=8)
            ),
            ft.NavigationRailDestination(
                icon=ft.Icons.LOCATION_ON_OUTLINED,
                selected_icon=ft.Icons.LOCATION_ON,
                label="Locations",
                padding=ft.padding.symmetric(vertical=8)
            ),
            ft.NavigationRailDestination(
                icon=ft.Icons.ACCOUNT_BALANCE_OUTLINED,
                selected_icon=ft.Icons.ACCOUNT_BALANCE,
                label="Financial",
                padding=ft.padding.symmetric(vertical=8)
            ),
            ft.NavigationRailDestination(
                icon=ft.Icons.VERIFIED_OUTLINED,
                selected_icon=ft.Icons.VERIFIED,
                label="Validation",
                padding=ft.padding.symmetric(vertical=8)
            ),
            ft.NavigationRailDestination(
                icon=ft.Icons.TUNE_OUTLINED,
                selected_icon=ft.Icons.TUNE,
                label="Sensitivity",
                padding=ft.padding.symmetric(vertical=8)
            ),
            ft.NavigationRailDestination(
                icon=ft.Icons.CASINO_OUTLINED,
                selected_icon=ft.Icons.CASINO,
                label="Monte Carlo",
                padding=ft.padding.symmetric(vertical=8)
            ),
            ft.NavigationRailDestination(
                icon=ft.Icons.COMPARE_OUTLINED,
                selected_icon=ft.Icons.COMPARE,
                label="Scenarios",
                padding=ft.padding.symmetric(vertical=8)
            ),
            ft.NavigationRailDestination(
                icon=ft.Icons.DOWNLOAD_OUTLINED,
                selected_icon=ft.Icons.DOWNLOAD,
                label="Export",
                padding=ft.padding.symmetric(vertical=8)
            )
        ]

        self.navigation_rail = ft.NavigationRail(
            selected_index=0,
            label_type=ft.NavigationRailLabelType.ALL,
            min_width=120,
            min_extended_width=220,
            destinations=destinations,
            on_change=self._on_navigation_change,
            bgcolor=ft.Colors.WHITE,
            indicator_color=ft.Colors.BLUE_100,
            indicator_shape=ft.RoundedRectangleBorder(radius=12),
            elevation=2
        )
    
    def _create_main_content(self):
        """Create modern main content area with breadcrumbs."""
        # Breadcrumb navigation
        self.breadcrumb = ft.Row([
            ft.Icon(ft.Icons.HOME, size=16, color=ft.Colors.GREY_600),
            ft.Text("Enhanced Financial Model", size=14, color=ft.Colors.GREY_600),
            ft.Text(" > ", size=14, color=ft.Colors.GREY_400),
            ft.Text("Setup", size=14, color=ft.Colors.BLUE_600, weight=ft.FontWeight.W_500)
        ], spacing=4)

        # Main content container with modern styling
        self.main_content = ft.Container(
            content=ft.Column([
                # Breadcrumb header
                ft.Container(
                    content=self.breadcrumb,
                    padding=ft.padding.only(left=20, right=20, top=15, bottom=10),
                    bgcolor=ft.Colors.WHITE,
                    border=ft.border.only(bottom=ft.BorderSide(1, ft.Colors.GREY_200))
                ),
                # Main content area
                ft.Container(
                    content=ft.Text("Loading...", text_align=ft.TextAlign.CENTER),
                    expand=True,
                    padding=25,
                    bgcolor=ft.Colors.GREY_50
                )
            ]),
            expand=True
        )
    
    def _create_status_bar(self):
        """Create enhanced status bar with health monitoring."""
        self.progress_bar = ft.ProgressBar(visible=False)

        # Health status indicator
        self.health_indicator = ft.Icon(
            ft.Icons.HEALTH_AND_SAFETY,
            color=ft.Colors.GREEN,
            size=16,
            tooltip="System Health: All services operational"
        )

        # Enhanced features indicators
        self.enhanced_indicators = ft.Row([
            ft.Icon(ft.Icons.MEMORY, size=14, color=ft.Colors.BLUE_600, tooltip="ML Predictions Active"),
            ft.Icon(ft.Icons.VIEW_IN_AR, size=14, color=ft.Colors.PURPLE_600, tooltip="3D Charts Available"),
            ft.Icon(ft.Icons.SAVE, size=14, color=ft.Colors.GREEN_600, tooltip="Auto-Save Enabled"),
            ft.Icon(ft.Icons.UNDO, size=14, color=ft.Colors.ORANGE_600, tooltip="Undo/Redo Available")
        ], spacing=4, visible=True)

        self.status_bar = ft.Container(
            content=ft.Column([
                self.progress_bar,
                ft.Row([
                    ft.Row([
                        ft.Text("Ready", size=12, color=ft.Colors.GREY_600),
                        self.health_indicator,
                        ft.VerticalDivider(width=1),
                        self.enhanced_indicators
                    ], spacing=8),
                    ft.Text("Enhanced Financial Model v3.0 • Agevolami SRL • 🚀 All Advanced Features Active",
                           size=12, color=ft.Colors.GREY_600, expand=True,
                           text_align=ft.TextAlign.RIGHT)
                ])
            ]),
            padding=ft.padding.symmetric(horizontal=20, vertical=10),
            bgcolor=ft.Colors.GREY_100,
            height=60
        )

        # Start periodic health checks
        self.page.run_task(self._periodic_health_check)
    
    def _on_keyboard_event(self, e: ft.KeyboardEvent):
        """Handle keyboard shortcuts for enhanced features."""
        try:
            # Undo: Ctrl+Z
            if e.key == "Z" and e.ctrl and not e.shift:
                if self.enhanced_service.undo_redo_service:
                    result = self.enhanced_service.undo_redo_service.undo()
                    if result is not None:
                        self.show_success("Action undone")
                        self.refresh_current_view()
                    else:
                        self.show_error("Nothing to undo")
                e.prevent_default = True
            
            # Redo: Ctrl+Shift+Z or Ctrl+Y
            elif ((e.key == "Z" and e.ctrl and e.shift) or 
                  (e.key == "Y" and e.ctrl)):
                if self.enhanced_service.undo_redo_service:
                    result = self.enhanced_service.undo_redo_service.redo()
                    if result is not None:
                        self.show_success("Action redone")
                        self.refresh_current_view()
                    else:
                        self.show_error("Nothing to redo")
                e.prevent_default = True
            
            # Save: Ctrl+S
            elif e.key == "S" and e.ctrl:
                if self.enhanced_service.persistence_service:
                    project_id = self.app_state.client_profile.get_clean_company_name()
                    version_id = self.enhanced_service.save_project_with_versioning(
                        project_id=project_id,
                        project_data={
                            'client_profile': self.app_state.client_profile.to_dict(),
                            'assumptions': self.app_state.project_assumptions.to_dict(),
                            'results': self.app_state.financial_results
                        }
                    )
                    self.show_success(f"Project saved (version: {version_id})")
                e.prevent_default = True
                
        except Exception as ex:
            self.logger.error(f"Keyboard shortcut error: {ex}")
    
    def refresh_current_view(self):
        """Refresh the currently active view."""
        current_tab = self.ui_state.current_tab
        if current_tab in self.views:
            view = self.views[current_tab]
            if hasattr(view, 'refresh'):
                view.refresh()
    
    def _on_navigation_change(self, e):
        """Handle navigation rail selection change."""
        tab_mapping = [
            TabState.PROJECT_SETUP,
            TabState.DASHBOARD,
            TabState.LOCATION_COMPARISON,
            TabState.FINANCIAL_MODEL,
            TabState.VALIDATION,
            TabState.SENSITIVITY,
            TabState.MONTE_CARLO,
            TabState.SCENARIOS,
            TabState.EXPORT
        ]
        
        if 0 <= e.control.selected_index < len(tab_mapping):
            selected_tab = tab_mapping[e.control.selected_index]
            self.navigate_to_tab(selected_tab)
    
    def navigate_to_tab(self, tab: TabState):
        """Navigate to a specific tab with modern visual feedback."""
        # Check if navigation is allowed
        if not self.ui_state.can_navigate_to_tab(tab):
            self.show_error("Please complete the required steps before accessing this tab")
            return

        # Show loading state during transition
        self.set_loading(True, "Loading...")

        # Update UI state
        self.ui_state.navigate_to_tab(tab)

        # Update navigation rail with animation
        tab_index = list(TabState).index(tab)
        self.navigation_rail.selected_index = tab_index

        # Update breadcrumb
        self._update_breadcrumb(tab)

        # Update main content with smooth transition
        if tab in self.views:
            view = self.views[tab]
            content = view.get_content()
            # Update the main content area (second child of the column)
            self.main_content.content.controls[1].content = content
        else:
            self.main_content.content.controls[1].content = ft.Text(
                f"View for {tab.value} not implemented yet",
                text_align=ft.TextAlign.CENTER,
                size=16,
                color=ft.Colors.GREY_600
            )

        # Clear loading state
        self.set_loading(False)

        self.page.update()
        self.logger.info(f"Navigated to tab: {tab.value}")

    def _update_breadcrumb(self, tab: TabState):
        """Update breadcrumb navigation."""
        tab_names = {
            TabState.PROJECT_SETUP: "Project Setup",
            TabState.DASHBOARD: "Dashboard",
            TabState.LOCATION_COMPARISON: "Location Comparison",
            TabState.FINANCIAL_MODEL: "Financial Model",
            TabState.VALIDATION: "Validation",
            TabState.SENSITIVITY: "Sensitivity Analysis",
            TabState.MONTE_CARLO: "Monte Carlo Simulation",
            TabState.SCENARIOS: "Scenario Analysis",
            TabState.EXPORT: "Export & Reports"
        }

        # Update the last breadcrumb item
        if len(self.breadcrumb.controls) >= 4:
            self.breadcrumb.controls[3].value = tab_names.get(tab, tab.value)
            self.breadcrumb.controls[3].color = ft.Colors.BLUE_600
    
    def handle_data_change(self, data_type: str, data: Any):
        """Handle data changes from views."""
        if data_type == "client_profile":
            self.app_state.client_profile = data
        elif data_type == "project_assumptions":
            self.app_state.project_assumptions = data
        
        self.logger.info(f"Data changed: {data_type}")
    
    def handle_action_request(self, action: str, params: Dict[str, Any]):
        """Handle action requests from views."""
        self.logger.info(f"Action requested: {action}")
        
        try:
            if action == "run_financial_model":
                self.page.run_task(self._run_financial_model)
            elif action == "run_comprehensive_analysis":
                self.page.run_task(self._run_comprehensive_analysis, params)
            elif action == "run_location_comparison":
                self.page.run_task(self._run_location_comparison, params)
            elif action == "run_sensitivity_analysis":
                self.page.run_task(self._run_sensitivity_analysis, params)
            elif action == "run_monte_carlo":
                self.page.run_task(self._run_monte_carlo, params)
            elif action == "run_scenario_analysis":
                self.page.run_task(self._run_scenario_analysis, params)
            elif action == "quick_export":
                self.page.run_task(self._quick_export, params)
            elif action == "comprehensive_export":
                self.page.run_task(self._comprehensive_export, params)
            elif action == "save_configuration":
                self._save_configuration(params)
            elif action == "load_preset":
                self._load_preset()
            else:
                self.show_error(f"Unknown action: {action}")
        
        except Exception as e:
            self.logger.error(f"Error handling action {action}: {str(e)}")
            self.show_error(f"Error: {str(e)}")
    
    async def _run_financial_model(self):
        """Run the financial model."""
        try:
            self.set_loading(True, "Running financial model...")
            
            def progress_callback(progress: float, message: str):
                self.update_progress(progress, message)
            
            # Run financial model
            results = self.financial_service.run_financial_model(
                self.app_state.project_assumptions,
                progress_callback
            )
            
            # Update state
            self.app_state.financial_results = results
            self.ui_state.set_model_executed(True)
            
            # Update views with results
            self._update_views_with_results()
            
            self.set_loading(False)
            self.show_success("Financial model completed successfully!")
            
        except Exception as e:
            self.set_loading(False)
            self.show_error(f"Error running financial model: {str(e)}")
    
    async def _run_comprehensive_analysis(self, params: Dict[str, Any]):
        """Run enhanced comprehensive analysis with all advanced features."""
        try:
            self.set_loading(True, "Starting enhanced comprehensive analysis...")
            
            def progress_callback(progress: float, message: str):
                self.update_progress(progress, message)
            
            # Step 1: Run enhanced financial model with ML predictions and Monte Carlo
            progress_callback(10, "Running enhanced financial model with ML predictions...")
            
            project_data = {
                'client_profile': self.app_state.client_profile.to_dict(),
                'assumptions': self.app_state.project_assumptions.to_dict()
            }
            
            enhanced_results = self.enhanced_service.run_enhanced_financial_model(
                project_data=project_data,
                include_ml_predictions=True,
                include_monte_carlo=True
            )
            
            # Step 2: Generate 3D charts
            progress_callback(40, "Generating interactive 3D visualizations...")
            
            charts_3d = self.enhanced_service.generate_advanced_charts(
                financial_results=enhanced_results,
                project_name=self.app_state.client_profile.project_name or "Solar Project"
            )
            
            # Step 3: Auto-save with versioning
            progress_callback(60, "Saving project with versioning...")
            
            if self.enhanced_service.persistence_service:
                project_id = self.app_state.client_profile.get_clean_company_name()
                version_id = self.enhanced_service.save_project_with_versioning(
                    project_id=project_id,
                    project_data={
                        'client_profile': self.app_state.client_profile.to_dict(),
                        'assumptions': self.app_state.project_assumptions.to_dict(),
                        'enhanced_results': enhanced_results,
                        'charts_3d': charts_3d
                    }
                )
                self.logger.info(f"Project saved with version: {version_id}")
            
            # Step 4: Run standard comprehensive report for exports
            progress_callback(70, "Generating comprehensive reports...")
            
            standard_results = self.report_service.generate_comprehensive_report(
                client_profile=self.app_state.client_profile,
                assumptions=self.app_state.project_assumptions,
                progress_callback=lambda p, m: progress_callback(70 + p*0.25, m)
            )
            
            # Step 5: Merge enhanced and standard results
            progress_callback(95, "Finalizing enhanced results...")
            
            # Update state with enhanced results
            self.app_state.financial_results = enhanced_results
            self.app_state.validation_results = standard_results['analysis_results'].get('validation')
            self.app_state.location_comparison_results = standard_results['analysis_results'].get('location_comparison')
            self.app_state.sensitivity_results = standard_results['analysis_results'].get('sensitivity')
            self.app_state.monte_carlo_results = enhanced_results.get('monte_carlo')
            self.app_state.scenario_results = standard_results['analysis_results'].get('scenarios')
            
            # Store enhanced features
            self.app_state.ml_predictions = enhanced_results.get('ml_predictions')
            self.app_state.charts_3d = charts_3d
            
            # Update UI state
            self.ui_state.set_model_executed(True)
            
            # Update views with enhanced data
            self._update_views_with_enhanced_results(enhanced_results, charts_3d)
            
            # Update export view with generated files
            if TabState.EXPORT in self.views:
                export_view = self.views[TabState.EXPORT]
                for file_type, file_path in standard_results['generated_files']:
                    export_view.add_generated_file(file_type, file_path)
            
            progress_callback(100, "Enhanced analysis completed!")
            self.set_loading(False)
            
            # Enhanced success message
            ml_status = "✓" if enhanced_results.get('ml_predictions') else "✗"
            charts_3d_count = len([k for k in charts_3d.keys() if '3d' in k.lower()])
            cache_status = "✓" if self.enhanced_service.cache_service else "✗"
            
            success_msg = (f"🚀 Enhanced Analysis Completed!\n\n"
                          f"📊 Features Used:\n"
                          f"  {ml_status} ML Predictions: {len(enhanced_results.get('ml_predictions', {}).get('predictions', {}))}/3 models\n"
                          f"  ✓ 3D Charts: {charts_3d_count} interactive visualizations\n"
                          f"  {cache_status} Performance Cache: {'Enabled' if self.enhanced_service.cache_service else 'Disabled'}\n"
                          f"  ✓ Auto-Save: Project versioned and backed up\n"
                          f"  ✓ Error Recovery: Comprehensive fallback systems\n\n"
                          f"📁 Generated: {len(standard_results['generated_files'])} files")
            
            self.show_success(success_msg)
            
        except Exception as e:
            self.set_loading(False)
            self.logger.error(f"Enhanced analysis failed: {e}")
            
            # Enhanced error recovery
            if self.enhanced_service.recovery_service:
                try:
                    fallback_results = self.enhanced_service._get_fallback_results()
                    self.app_state.financial_results = fallback_results
                    self.show_error(f"Analysis failed, using fallback data: {str(e)}")
                except:
                    self.show_error(f"Error in enhanced comprehensive analysis: {str(e)}")
            else:
                self.show_error(f"Error in enhanced comprehensive analysis: {str(e)}")
    
    async def _run_location_comparison(self, params: Dict[str, Any]):
        """Run location comparison analysis."""
        try:
            self.set_loading(True, "Running location comparison...")
            
            locations = params.get('locations', [])
            results = self.location_service.compare_locations(
                self.app_state.project_assumptions,
                locations
            )
            
            self.app_state.location_comparison_results = results
            
            # Update location comparison view
            if TabState.LOCATION_COMPARISON in self.views:
                self.views[TabState.LOCATION_COMPARISON].set_comparison_results(results)
            
            self.set_loading(False)
            self.show_success("Location comparison completed!")
            
        except Exception as e:
            self.set_loading(False)
            self.show_error(f"Error in location comparison: {str(e)}")
    
    async def _run_sensitivity_analysis(self, params: Dict[str, Any]):
        """Run sensitivity analysis."""
        try:
            self.set_loading(True, "Running sensitivity analysis...")
            
            variables = params.get('variables', [])
            results = self.financial_service.run_sensitivity_analysis(
                self.app_state.project_assumptions,
                variables
            )
            
            self.app_state.sensitivity_results = results
            
            # Update sensitivity view
            if TabState.SENSITIVITY in self.views:
                self.views[TabState.SENSITIVITY].set_sensitivity_results(results)
            
            self.set_loading(False)
            self.show_success("Sensitivity analysis completed!")
            
        except Exception as e:
            self.set_loading(False)
            self.show_error(f"Error in sensitivity analysis: {str(e)}")
    
    async def _run_monte_carlo(self, params: Dict[str, Any]):
        """Run Monte Carlo simulation."""
        try:
            self.set_loading(True, "Running Monte Carlo simulation...")
            
            n_simulations = params.get('n_simulations', 1000)
            
            def progress_callback(progress: float, message: str):
                self.update_progress(progress, message)
                # Update Monte Carlo view progress
                if TabState.MONTE_CARLO in self.views:
                    self.views[TabState.MONTE_CARLO].set_monte_carlo_results({
                        'progress': progress,
                        'in_progress': True
                    })
            
            results = self.financial_service.run_monte_carlo_simulation(
                self.app_state.project_assumptions,
                n_simulations,
                progress_callback
            )
            
            self.app_state.monte_carlo_results = results
            
            # Update Monte Carlo view
            if TabState.MONTE_CARLO in self.views:
                self.views[TabState.MONTE_CARLO].set_monte_carlo_results(results)
            
            self.set_loading(False)
            self.show_success(f"Monte Carlo simulation completed with {n_simulations} simulations!")
            
        except Exception as e:
            self.set_loading(False)
            self.show_error(f"Error in Monte Carlo simulation: {str(e)}")
    
    async def _run_scenario_analysis(self, params: Dict[str, Any]):
        """Run scenario analysis."""
        try:
            self.set_loading(True, "Running scenario analysis...")

            scenarios = params.get('scenarios', ["Base", "Optimistic", "Pessimistic"])

            def progress_callback(progress: float, message: str):
                self.update_progress(progress, message)

            results = self.financial_service.run_scenario_analysis(
                self.app_state.project_assumptions,
                scenarios,
                progress_callback
            )

            self.app_state.scenario_results = results

            # Update scenarios view
            if TabState.SCENARIOS in self.views:
                self.views[TabState.SCENARIOS].set_scenario_results(results)

            self.set_loading(False)
            self.show_success(f"Scenario analysis completed for {len(scenarios)} scenarios!")

        except Exception as e:
            self.set_loading(False)
            self.show_error(f"Error in scenario analysis: {str(e)}")
    
    async def _quick_export(self, params: Dict[str, Any]):
        """Perform quick export."""
        try:
            self.set_loading(True, "Exporting data...")
            
            formats = params.get('formats', ['Excel'])
            
            # Export based on selected formats
            generated_files = []
            
            if 'Excel' in formats:
                excel_file = self.export_service.export_excel_report(
                    self.app_state.client_profile,
                    self.app_state.project_assumptions,
                    self.app_state.financial_results or {}
                )
                generated_files.append(('Excel Report', excel_file))
            
            # Update export view
            if TabState.EXPORT in self.views:
                export_view = self.views[TabState.EXPORT]
                for file_type, file_path in generated_files:
                    export_view.add_generated_file(file_type, file_path)
            
            self.set_loading(False)
            self.show_success(f"Export completed! Generated {len(generated_files)} files.")
            
        except Exception as e:
            self.set_loading(False)
            self.show_error(f"Error during export: {str(e)}")
    
    async def _comprehensive_export(self, params: Dict[str, Any]):
        """Perform comprehensive export."""
        # Similar to quick export but with all analysis included
        await self._quick_export(params)
    
    def _save_configuration(self, params: Dict[str, Any]):
        """Save current configuration."""
        try:
            # Save configuration logic here
            self.show_success("Configuration saved successfully!")
        except Exception as e:
            self.show_error(f"Error saving configuration: {str(e)}")
    
    def _load_preset(self):
        """Load preset configuration."""
        try:
            # Load preset logic here
            self.show_success("Preset loaded successfully!")
        except Exception as e:
            self.show_error(f"Error loading preset: {str(e)}")
    
    def _update_views_with_results(self):
        """Update all views with current results."""
        # Update dashboard
        if TabState.DASHBOARD in self.views and self.app_state.financial_results:
            self.views[TabState.DASHBOARD].set_financial_results(self.app_state.financial_results)
        
        # Update financial model view
        if TabState.FINANCIAL_MODEL in self.views and self.app_state.financial_results:
            self.views[TabState.FINANCIAL_MODEL].set_financial_results(self.app_state.financial_results)
        
        # Update validation view
        if TabState.VALIDATION in self.views and self.app_state.validation_results:
            self.views[TabState.VALIDATION].set_validation_results(self.app_state.validation_results)
    
    def _update_views_with_enhanced_results(self, enhanced_results: Dict[str, Any], charts_3d: Dict[str, str]):
        """Update all views with enhanced results including ML predictions and 3D charts."""
        
        # Update all standard views first
        self._update_views_with_results()
        
        # Update dashboard with ML predictions
        if TabState.DASHBOARD in self.views:
            dashboard_view = self.views[TabState.DASHBOARD]
            if hasattr(dashboard_view, 'set_ml_predictions') and enhanced_results.get('ml_predictions'):
                dashboard_view.set_ml_predictions(enhanced_results['ml_predictions'])
            if hasattr(dashboard_view, 'set_3d_charts') and charts_3d:
                dashboard_view.set_3d_charts(charts_3d)
        
        # Update financial model view with enhanced data
        if TabState.FINANCIAL_MODEL in self.views:
            financial_view = self.views[TabState.FINANCIAL_MODEL]
            if hasattr(financial_view, 'set_enhanced_results'):
                financial_view.set_enhanced_results(enhanced_results)
        
        # Update Monte Carlo view with enhanced simulation
        if TabState.MONTE_CARLO in self.views and enhanced_results.get('monte_carlo'):
            mc_view = self.views[TabState.MONTE_CARLO]
            if hasattr(mc_view, 'set_monte_carlo_results'):
                mc_view.set_monte_carlo_results(enhanced_results['monte_carlo'])
        
        # Update location comparison view
        if TabState.LOCATION_COMPARISON in self.views and self.app_state.location_comparison_results:
            location_view = self.views[TabState.LOCATION_COMPARISON]
            if hasattr(location_view, 'set_comparison_results'):
                location_view.set_comparison_results(self.app_state.location_comparison_results)
        
        self.logger.info("Views updated with enhanced results including ML predictions and 3D charts")
    
    def update_status(self, status: str, message: str):
        """Update status bar."""
        if self.status_bar:
            status_text = self.status_bar.content.controls[1].controls[0]
            status_text.value = message
            self.page.update()
    
    def set_loading(self, loading: bool, message: str = ""):
        """Set loading state."""
        self.ui_state.set_loading(loading, message)
        
        if self.progress_bar:
            self.progress_bar.visible = loading
        
        if message:
            self.update_status("loading" if loading else "ready", message)
        
        self.page.update()
    
    def update_progress(self, progress: float, message: str = ""):
        """Update progress."""
        self.ui_state.update_progress(progress, message)
        
        if self.progress_bar:
            self.progress_bar.value = progress / 100.0
        
        if message:
            self.update_status("loading", f"{message} ({progress:.0f}%)")
        
        self.page.update()
    
    def show_error(self, error: str):
        """Show error message."""
        self.ui_state.set_error(error)
        self.update_status("error", f"Error: {error}")
        
        # Show snack bar
        snack_bar = ft.SnackBar(
            content=ft.Text(error),
            bgcolor=ft.Colors.RED_400,
            open=True
        )
        self.page.snack_bar = snack_bar
        self.page.update()
    
    def show_success(self, message: str):
        """Show success message."""
        self.ui_state.clear_error()
        self.update_status("success", message)
        
        # Show snack bar
        snack_bar = ft.SnackBar(
            content=ft.Text(message),
            bgcolor=ft.Colors.GREEN_400,
            open=True
        )
        self.page.snack_bar = snack_bar
        self.page.update()

    async def _periodic_health_check(self):
        """Perform periodic health checks and update status indicator."""
        import asyncio

        while True:
            try:
                # Wait 30 seconds between checks
                await asyncio.sleep(30)

                # Get health status
                health_status = get_health_status()

                # Update health indicator based on system status
                system_status = health_status.get('system_status')

                if system_status and hasattr(system_status, 'value'):
                    status_value = system_status.value
                else:
                    status_value = str(system_status).lower() if system_status else 'unknown'

                if status_value == 'healthy':
                    self.health_indicator.color = ft.Colors.GREEN
                    self.health_indicator.name = ft.Icons.HEALTH_AND_SAFETY
                    tooltip = f"System Health: All {health_status.get('total_services', 0)} services operational"
                elif status_value == 'warning':
                    self.health_indicator.color = ft.Colors.ORANGE
                    self.health_indicator.name = ft.Icons.WARNING
                    tooltip = f"System Health: {health_status.get('warning_services', 0)} services have warnings"
                elif status_value == 'critical':
                    self.health_indicator.color = ft.Colors.RED
                    self.health_indicator.name = ft.Icons.ERROR
                    tooltip = f"System Health: {health_status.get('critical_services', 0)} services critical"
                else:
                    self.health_indicator.color = ft.Colors.GREY
                    self.health_indicator.name = ft.Icons.HELP
                    tooltip = "System Health: Status unknown"

                self.health_indicator.tooltip = tooltip

                # Update UI if page is still active
                if hasattr(self.page, 'update'):
                    self.health_indicator.update()

                self.logger.debug(f"Health check completed: {status_value}")

            except Exception as e:
                self.logger.error(f"Health check failed: {str(e)}")
                # Set error state
                if hasattr(self, 'health_indicator'):
                    self.health_indicator.color = ft.Colors.RED
                    self.health_indicator.name = ft.Icons.ERROR
                    self.health_indicator.tooltip = f"Health check failed: {str(e)}"
                    try:
                        self.health_indicator.update()
                    except:
                        pass
