"""
Error Recovery Service
======================

Advanced error recovery with circuit breaker pattern, multiple recovery strategies,
and graceful degradation capabilities.
"""

import time
import logging
import threading
from typing import Dict, Any, Optional, Callable, List, Union
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, field
from functools import wraps
import traceback


class RecoveryStrategy(Enum):
    """Available recovery strategies."""
    RETRY = "retry"
    FALLBACK = "fallback"
    DEGRADE = "degrade"
    FAIL_SAFE = "fail_safe"


class CircuitState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Circuit is open, requests fail fast
    HALF_OPEN = "half_open"  # Testing if service recovered


@dataclass
class RecoveryConfig:
    """Configuration for error recovery."""
    max_retries: int = 3
    retry_delay: float = 1.0
    exponential_backoff: bool = True
    backoff_multiplier: float = 2.0
    max_retry_delay: float = 60.0
    circuit_breaker_enabled: bool = True
    failure_threshold: int = 5
    recovery_timeout: int = 60
    success_threshold: int = 2


@dataclass
class ErrorContext:
    """Context information for error recovery."""
    error: Exception
    function_name: str
    args: tuple = field(default_factory=tuple)
    kwargs: dict = field(default_factory=dict)
    attempt_number: int = 0
    timestamp: datetime = field(default_factory=datetime.now)
    recovery_strategy: Optional[RecoveryStrategy] = None
    additional_info: dict = field(default_factory=dict)


class CircuitBreaker:
    """Circuit breaker implementation for service protection."""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60,
                 success_threshold: int = 2):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.success_threshold = success_threshold
        
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = None
        self.lock = threading.RLock()
        
        self.logger = logging.getLogger(__name__)
    
    def call(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with circuit breaker protection."""
        with self.lock:
            if self.state == CircuitState.OPEN:
                if self._should_attempt_reset():
                    self.state = CircuitState.HALF_OPEN
                    self.logger.info("Circuit breaker transitioning to HALF_OPEN")
                else:
                    raise Exception("Circuit breaker is OPEN - service unavailable")
            
            try:
                result = func(*args, **kwargs)
                self._on_success()
                return result
                
            except Exception as e:
                self._on_failure()
                raise
    
    def _should_attempt_reset(self) -> bool:
        """Check if circuit should attempt to reset."""
        if self.last_failure_time is None:
            return True
        return (datetime.now() - self.last_failure_time).seconds >= self.recovery_timeout
    
    def _on_success(self):
        """Handle successful operation."""
        if self.state == CircuitState.HALF_OPEN:
            self.success_count += 1
            if self.success_count >= self.success_threshold:
                self.state = CircuitState.CLOSED
                self.failure_count = 0
                self.success_count = 0
                self.logger.info("Circuit breaker reset to CLOSED")
        elif self.state == CircuitState.CLOSED:
            self.failure_count = 0
    
    def _on_failure(self):
        """Handle failed operation."""
        self.failure_count += 1
        self.last_failure_time = datetime.now()
        
        if self.state == CircuitState.CLOSED and self.failure_count >= self.failure_threshold:
            self.state = CircuitState.OPEN
            self.logger.warning(f"Circuit breaker opened after {self.failure_count} failures")
        elif self.state == CircuitState.HALF_OPEN:
            self.state = CircuitState.OPEN
            self.success_count = 0
            self.logger.warning("Circuit breaker returned to OPEN from HALF_OPEN")
    
    def get_state(self) -> Dict[str, Any]:
        """Get circuit breaker state information."""
        with self.lock:
            return {
                'state': self.state.value,
                'failure_count': self.failure_count,
                'success_count': self.success_count,
                'last_failure_time': self.last_failure_time.isoformat() if self.last_failure_time else None
            }


class RetryManager:
    """Manages retry logic with exponential backoff."""
    
    def __init__(self, config: RecoveryConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    def execute_with_retry(self, func: Callable, error_context: ErrorContext) -> Any:
        """Execute function with retry logic."""
        last_exception = None
        
        for attempt in range(self.config.max_retries + 1):
            try:
                if attempt > 0:
                    delay = self._calculate_delay(attempt)
                    self.logger.info(f"Retrying {error_context.function_name} (attempt {attempt + 1}/{self.config.max_retries + 1}) after {delay}s delay")
                    time.sleep(delay)
                
                error_context.attempt_number = attempt + 1
                return func(*error_context.args, **error_context.kwargs)
                
            except Exception as e:
                last_exception = e
                error_context.error = e
                
                if attempt < self.config.max_retries:
                    if self._should_retry(e, attempt):
                        self.logger.warning(f"Attempt {attempt + 1} failed for {error_context.function_name}: {e}")
                        continue
                    else:
                        self.logger.error(f"Non-retryable error for {error_context.function_name}: {e}")
                        break
                else:
                    self.logger.error(f"All retry attempts exhausted for {error_context.function_name}")
                    break
        
        # All retries exhausted or non-retryable error
        raise last_exception
    
    def _calculate_delay(self, attempt: int) -> float:
        """Calculate delay for retry attempt."""
        if not self.config.exponential_backoff:
            return self.config.retry_delay
        
        delay = self.config.retry_delay * (self.config.backoff_multiplier ** (attempt - 1))
        return min(delay, self.config.max_retry_delay)
    
    def _should_retry(self, error: Exception, attempt: int) -> bool:
        """Determine if error should be retried."""
        # Don't retry certain types of errors
        non_retryable_errors = (
            ValueError,      # Bad input data
            TypeError,       # Programming errors
            AttributeError,  # Programming errors
            KeyError,        # Programming errors
            NotImplementedError,  # Programming errors
        )
        
        if isinstance(error, non_retryable_errors):
            return False
        
        # Can add more sophisticated retry logic here
        return True


class FallbackManager:
    """Manages fallback strategies and graceful degradation."""
    
    def __init__(self):
        self.fallback_strategies: Dict[str, Callable] = {}
        self.logger = logging.getLogger(__name__)
    
    def register_fallback(self, service_name: str, fallback_func: Callable):
        """Register fallback function for a service."""
        self.fallback_strategies[service_name] = fallback_func
        self.logger.info(f"Registered fallback strategy for {service_name}")
    
    def execute_fallback(self, service_name: str, error_context: ErrorContext) -> Any:
        """Execute fallback strategy for service."""
        if service_name not in self.fallback_strategies:
            raise Exception(f"No fallback strategy registered for {service_name}")
        
        fallback_func = self.fallback_strategies[service_name]
        
        try:
            self.logger.info(f"Executing fallback strategy for {service_name}")
            return fallback_func(error_context)
        except Exception as e:
            self.logger.error(f"Fallback strategy failed for {service_name}: {e}")
            raise
    
    def get_default_financial_data(self, error_context: ErrorContext) -> Dict[str, Any]:
        """Default fallback for financial model failures."""
        return {
            'kpis': {
                'irr_equity': 0.08,
                'npv_equity': 1000000,
                'lcoe': 0.06,
                'payback_years': 12
            },
            'cashflow': {},
            'status': 'fallback_data',
            'error_message': f"Using fallback data due to: {error_context.error}",
            'timestamp': datetime.now().isoformat()
        }
    
    def get_default_location_data(self, error_context: ErrorContext) -> Dict[str, Any]:
        """Default fallback for location service failures."""
        return {
            'locations': [],
            'comparison_results': {},
            'status': 'fallback_data',
            'error_message': f"Using fallback data due to: {error_context.error}",
            'timestamp': datetime.now().isoformat()
        }
    
    def get_degraded_export_response(self, error_context: ErrorContext) -> Dict[str, Any]:
        """Degraded response for export service failures."""
        return {
            'export_status': 'degraded',
            'available_formats': ['json'],  # Only basic format available
            'error_message': f"Export service degraded due to: {error_context.error}",
            'timestamp': datetime.now().isoformat()
        }


class ErrorRecoveryService:
    """Comprehensive error recovery service with multiple strategies."""
    
    def __init__(self, config: Optional[RecoveryConfig] = None):
        self.config = config or RecoveryConfig()
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.retry_manager = RetryManager(self.config)
        self.fallback_manager = FallbackManager()
        
        # Statistics
        self.stats_lock = threading.Lock()
        self.recovery_stats = {
            'total_recoveries': 0,
            'successful_retries': 0,
            'fallback_executions': 0,
            'circuit_breaker_trips': 0,
            'recovery_strategies_used': {strategy.value: 0 for strategy in RecoveryStrategy}
        }
        
        # Setup default fallback strategies
        self._setup_default_fallbacks()
        
        self.logger.info("Error recovery service initialized")
    
    def recover_from_error(self, error: Exception, service_name: str, 
                          func: Callable, *args, **kwargs) -> Any:
        """Main recovery orchestration method."""
        error_context = ErrorContext(
            error=error,
            function_name=f"{service_name}.{func.__name__}",
            args=args,
            kwargs=kwargs
        )
        
        with self.stats_lock:
            self.recovery_stats['total_recoveries'] += 1
        
        # Determine recovery strategy
        strategy = self._determine_recovery_strategy(error, service_name)
        error_context.recovery_strategy = strategy
        
        with self.stats_lock:
            self.recovery_stats['recovery_strategies_used'][strategy.value] += 1
        
        self.logger.info(f"Using {strategy.value} recovery strategy for {service_name}")
        
        try:
            if strategy == RecoveryStrategy.RETRY:
                return self._execute_retry_strategy(func, error_context, service_name)
            elif strategy == RecoveryStrategy.FALLBACK:
                return self._execute_fallback_strategy(service_name, error_context)
            elif strategy == RecoveryStrategy.DEGRADE:
                return self._execute_degradation_strategy(service_name, error_context)
            elif strategy == RecoveryStrategy.FAIL_SAFE:
                return self._execute_fail_safe_strategy(service_name, error_context)
            else:
                raise error  # No recovery possible
                
        except Exception as recovery_error:
            self.logger.error(f"Recovery failed for {service_name}: {recovery_error}")
            # Log the full error context for debugging
            self._log_error_context(error_context, recovery_error)
            raise recovery_error
    
    def get_circuit_breaker(self, service_name: str) -> CircuitBreaker:
        """Get or create circuit breaker for service."""
        if service_name not in self.circuit_breakers:
            self.circuit_breakers[service_name] = CircuitBreaker(
                failure_threshold=self.config.failure_threshold,
                recovery_timeout=self.config.recovery_timeout,
                success_threshold=self.config.success_threshold
            )
        return self.circuit_breakers[service_name]
    
    def register_fallback_strategy(self, service_name: str, fallback_func: Callable):
        """Register custom fallback strategy for a service."""
        self.fallback_manager.register_fallback(service_name, fallback_func)
    
    def get_recovery_stats(self) -> Dict[str, Any]:
        """Get recovery service statistics."""
        with self.stats_lock:
            stats = self.recovery_stats.copy()
        
        # Add circuit breaker states
        stats['circuit_breakers'] = {}
        for service_name, cb in self.circuit_breakers.items():
            stats['circuit_breakers'][service_name] = cb.get_state()
        
        return stats
    
    def _determine_recovery_strategy(self, error: Exception, service_name: str) -> RecoveryStrategy:
        """Determine the best recovery strategy for the error."""
        # Check circuit breaker state
        if self.config.circuit_breaker_enabled:
            cb = self.get_circuit_breaker(service_name)
            if cb.state == CircuitState.OPEN:
                return RecoveryStrategy.FALLBACK
        
        # Strategy based on error type
        if isinstance(error, (ConnectionError, TimeoutError)):
            return RecoveryStrategy.RETRY
        elif isinstance(error, (ValueError, TypeError)):
            return RecoveryStrategy.FAIL_SAFE
        elif isinstance(error, (FileNotFoundError, PermissionError)):
            return RecoveryStrategy.FALLBACK
        else:
            return RecoveryStrategy.DEGRADE
    
    def _execute_retry_strategy(self, func: Callable, error_context: ErrorContext, 
                               service_name: str) -> Any:
        """Execute retry strategy with circuit breaker protection."""
        if self.config.circuit_breaker_enabled:
            cb = self.get_circuit_breaker(service_name)
            
            def wrapped_func(*args, **kwargs):
                return cb.call(func, *args, **kwargs)
            
            result = self.retry_manager.execute_with_retry(wrapped_func, error_context)
        else:
            result = self.retry_manager.execute_with_retry(func, error_context)
        
        with self.stats_lock:
            self.recovery_stats['successful_retries'] += 1
        
        return result
    
    def _execute_fallback_strategy(self, service_name: str, error_context: ErrorContext) -> Any:
        """Execute fallback strategy."""
        with self.stats_lock:
            self.recovery_stats['fallback_executions'] += 1
        
        return self.fallback_manager.execute_fallback(service_name, error_context)
    
    def _execute_degradation_strategy(self, service_name: str, error_context: ErrorContext) -> Any:
        """Execute degradation strategy."""
        # Return degraded service response
        if service_name == "export_service":
            return self.fallback_manager.get_degraded_export_response(error_context)
        else:
            # Generic degraded response
            return {
                'status': 'degraded',
                'service': service_name,
                'error_message': f"Service operating in degraded mode: {error_context.error}",
                'timestamp': datetime.now().isoformat()
            }
    
    def _execute_fail_safe_strategy(self, service_name: str, error_context: ErrorContext) -> Any:
        """Execute fail-safe strategy."""
        # Log error and return safe default
        self.logger.error(f"Fail-safe activated for {service_name}: {error_context.error}")
        
        return {
            'status': 'fail_safe',
            'service': service_name,
            'message': 'Service failed safely with default response',
            'timestamp': datetime.now().isoformat()
        }
    
    def _setup_default_fallbacks(self):
        """Setup default fallback strategies for common services."""
        self.fallback_manager.register_fallback(
            "financial_service", 
            self.fallback_manager.get_default_financial_data
        )
        self.fallback_manager.register_fallback(
            "location_service", 
            self.fallback_manager.get_default_location_data
        )
        self.fallback_manager.register_fallback(
            "export_service", 
            self.fallback_manager.get_degraded_export_response
        )
    
    def _log_error_context(self, error_context: ErrorContext, recovery_error: Exception):
        """Log detailed error context for debugging."""
        self.logger.error(f"""
        Recovery Context:
        - Function: {error_context.function_name}
        - Original Error: {error_context.error}
        - Recovery Strategy: {error_context.recovery_strategy.value if error_context.recovery_strategy else 'None'}
        - Attempt Number: {error_context.attempt_number}
        - Recovery Error: {recovery_error}
        - Traceback: {traceback.format_exc()}
        """)


def recoverable(service_name: str, recovery_service: Optional[ErrorRecoveryService] = None):
    """Decorator to make functions recoverable from errors."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if recovery_service:
                    recovery = recovery_service
                else:
                    # Use global recovery service
                    recovery = get_recovery_service()
                
                return recovery.recover_from_error(e, service_name, func, *args, **kwargs)
        
        return wrapper
    return decorator


# Global recovery service instance
_recovery_service: Optional[ErrorRecoveryService] = None

def get_recovery_service() -> ErrorRecoveryService:
    """Get global recovery service instance."""
    global _recovery_service
    if _recovery_service is None:
        _recovery_service = ErrorRecoveryService()
    return _recovery_service


def initialize_recovery_service(config: Optional[RecoveryConfig] = None) -> ErrorRecoveryService:
    """Initialize global recovery service with custom configuration."""
    global _recovery_service
    _recovery_service = ErrorRecoveryService(config)
    return _recovery_service
