"""
Application Health Monitor - 2025 Edition
=========================================

Comprehensive health monitoring and service validation.
"""

import logging
import time
import traceback
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

class HealthStatus(Enum):
    """Health status levels."""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"

@dataclass
class HealthCheck:
    """Individual health check result."""
    name: str
    status: HealthStatus
    message: str
    duration_ms: float
    timestamp: datetime
    details: Optional[Dict[str, Any]] = None

@dataclass
class ServiceHealth:
    """Service health summary."""
    service_name: str
    overall_status: HealthStatus
    checks: List[HealthCheck]
    last_check: datetime
    uptime_percentage: float

class HealthMonitor:
    """Comprehensive application health monitor."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.health_history = []
        self.service_registry = {}
        self.check_interval = 60  # seconds
        self.last_full_check = None
        
    def register_service(self, service_name: str, health_check_func: Callable[[], Dict[str, Any]]):
        """Register a service for health monitoring."""
        self.service_registry[service_name] = {
            'check_function': health_check_func,
            'last_check': None,
            'status_history': [],
            'error_count': 0
        }
        self.logger.info(f"Registered service for health monitoring: {service_name}")
    
    def run_health_check(self, service_name: Optional[str] = None) -> Dict[str, ServiceHealth]:
        """Run health checks for specified service or all services."""
        results = {}
        
        services_to_check = [service_name] if service_name else list(self.service_registry.keys())
        
        for svc_name in services_to_check:
            if svc_name not in self.service_registry:
                continue
                
            try:
                service_health = self._check_service_health(svc_name)
                results[svc_name] = service_health
                
                # Update service registry
                self.service_registry[svc_name]['last_check'] = datetime.now()
                self.service_registry[svc_name]['status_history'].append({
                    'timestamp': datetime.now(),
                    'status': service_health.overall_status,
                    'checks': len(service_health.checks)
                })
                
                # Keep only last 100 status entries
                if len(self.service_registry[svc_name]['status_history']) > 100:
                    self.service_registry[svc_name]['status_history'] = \
                        self.service_registry[svc_name]['status_history'][-100:]
                
            except Exception as e:
                self.logger.error(f"Health check failed for {svc_name}: {str(e)}")
                self.service_registry[svc_name]['error_count'] += 1
                
                # Create error health check
                error_check = HealthCheck(
                    name=f"{svc_name}_error",
                    status=HealthStatus.CRITICAL,
                    message=f"Health check failed: {str(e)}",
                    duration_ms=0,
                    timestamp=datetime.now(),
                    details={'error': str(e), 'traceback': traceback.format_exc()}
                )
                
                results[svc_name] = ServiceHealth(
                    service_name=svc_name,
                    overall_status=HealthStatus.CRITICAL,
                    checks=[error_check],
                    last_check=datetime.now(),
                    uptime_percentage=0.0
                )
        
        self.last_full_check = datetime.now()
        return results
    
    def _check_service_health(self, service_name: str) -> ServiceHealth:
        """Check health of a specific service."""
        service_info = self.service_registry[service_name]
        check_function = service_info['check_function']
        
        checks = []
        start_time = time.time()
        
        try:
            # Run the service's health check function
            check_results = check_function()
            duration_ms = (time.time() - start_time) * 1000
            
            # Process check results
            if isinstance(check_results, dict):
                for check_name, result in check_results.items():
                    if isinstance(result, dict):
                        status = HealthStatus(result.get('status', 'unknown'))
                        message = result.get('message', 'No message')
                        details = result.get('details', {})
                    else:
                        # Simple boolean result
                        status = HealthStatus.HEALTHY if result else HealthStatus.CRITICAL
                        message = "Check passed" if result else "Check failed"
                        details = {}
                    
                    checks.append(HealthCheck(
                        name=check_name,
                        status=status,
                        message=message,
                        duration_ms=duration_ms,
                        timestamp=datetime.now(),
                        details=details
                    ))
            else:
                # Single boolean result
                status = HealthStatus.HEALTHY if check_results else HealthStatus.CRITICAL
                checks.append(HealthCheck(
                    name=f"{service_name}_basic",
                    status=status,
                    message="Basic health check",
                    duration_ms=duration_ms,
                    timestamp=datetime.now()
                ))
        
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            checks.append(HealthCheck(
                name=f"{service_name}_exception",
                status=HealthStatus.CRITICAL,
                message=f"Health check exception: {str(e)}",
                duration_ms=duration_ms,
                timestamp=datetime.now(),
                details={'error': str(e)}
            ))
        
        # Determine overall status
        overall_status = self._determine_overall_status(checks)
        
        # Calculate uptime percentage
        uptime_percentage = self._calculate_uptime_percentage(service_name)
        
        return ServiceHealth(
            service_name=service_name,
            overall_status=overall_status,
            checks=checks,
            last_check=datetime.now(),
            uptime_percentage=uptime_percentage
        )
    
    def _determine_overall_status(self, checks: List[HealthCheck]) -> HealthStatus:
        """Determine overall status from individual checks."""
        if not checks:
            return HealthStatus.UNKNOWN
        
        statuses = [check.status for check in checks]
        
        if HealthStatus.CRITICAL in statuses:
            return HealthStatus.CRITICAL
        elif HealthStatus.WARNING in statuses:
            return HealthStatus.WARNING
        elif all(status == HealthStatus.HEALTHY for status in statuses):
            return HealthStatus.HEALTHY
        else:
            return HealthStatus.WARNING
    
    def _calculate_uptime_percentage(self, service_name: str) -> float:
        """Calculate uptime percentage for a service."""
        try:
            history = self.service_registry[service_name]['status_history']
            if not history:
                return 100.0
            
            # Count healthy checks in the last 24 hours
            cutoff_time = datetime.now() - timedelta(hours=24)
            recent_checks = [h for h in history if h['timestamp'] > cutoff_time]
            
            if not recent_checks:
                return 100.0
            
            healthy_count = sum(1 for h in recent_checks 
                              if h['status'] == HealthStatus.HEALTHY)
            
            return (healthy_count / len(recent_checks)) * 100.0
            
        except Exception:
            return 0.0
    
    def get_system_health_summary(self) -> Dict[str, Any]:
        """Get overall system health summary."""
        all_health = self.run_health_check()
        
        total_services = len(all_health)
        healthy_services = sum(1 for h in all_health.values() 
                             if h.overall_status == HealthStatus.HEALTHY)
        warning_services = sum(1 for h in all_health.values() 
                             if h.overall_status == HealthStatus.WARNING)
        critical_services = sum(1 for h in all_health.values() 
                              if h.overall_status == HealthStatus.CRITICAL)
        
        # Determine overall system status
        if critical_services > 0:
            system_status = HealthStatus.CRITICAL
        elif warning_services > 0:
            system_status = HealthStatus.WARNING
        elif healthy_services == total_services:
            system_status = HealthStatus.HEALTHY
        else:
            system_status = HealthStatus.UNKNOWN
        
        # Calculate average uptime
        avg_uptime = sum(h.uptime_percentage for h in all_health.values()) / total_services if total_services > 0 else 0
        
        return {
            'system_status': system_status,
            'total_services': total_services,
            'healthy_services': healthy_services,
            'warning_services': warning_services,
            'critical_services': critical_services,
            'average_uptime': avg_uptime,
            'last_check': self.last_full_check,
            'services': {name: {
                'status': health.overall_status.value,
                'uptime': health.uptime_percentage,
                'last_check': health.last_check,
                'check_count': len(health.checks)
            } for name, health in all_health.items()}
        }

# Service-specific health check functions
def financial_service_health_check() -> Dict[str, Any]:
    """Health check for financial service."""
    try:
        from .financial_service import FinancialModelService
        from .enhanced_dcf_model import EnhancedDCFModel, EnhancedDCFAssumptions
        
        checks = {}
        
        # Test service instantiation
        try:
            service = FinancialModelService()
            checks['service_instantiation'] = {
                'status': 'healthy',
                'message': 'Service instantiated successfully'
            }
        except Exception as e:
            checks['service_instantiation'] = {
                'status': 'critical',
                'message': f'Service instantiation failed: {str(e)}'
            }
        
        # Test DCF model
        try:
            dcf_model = EnhancedDCFModel()
            test_assumptions = EnhancedDCFAssumptions()
            cashflow = dcf_model.build_cashflow(test_assumptions)
            checks['dcf_model'] = {
                'status': 'healthy',
                'message': 'DCF model working correctly',
                'details': {'cashflow_rows': len(cashflow)}
            }
        except Exception as e:
            checks['dcf_model'] = {
                'status': 'critical',
                'message': f'DCF model failed: {str(e)}'
            }
        
        return checks
        
    except ImportError as e:
        return {
            'import_error': {
                'status': 'critical',
                'message': f'Failed to import financial service: {str(e)}'
            }
        }

def location_service_health_check() -> Dict[str, Any]:
    """Health check for location service."""
    try:
        from .location_service import LocationService
        from models.location_config import LocationManager
        
        checks = {}
        
        # Test location manager
        try:
            location_manager = LocationManager()
            locations = location_manager.get_available_locations()
            checks['location_manager'] = {
                'status': 'healthy',
                'message': f'Location manager loaded {len(locations)} locations',
                'details': {'location_count': len(locations)}
            }
        except Exception as e:
            checks['location_manager'] = {
                'status': 'critical',
                'message': f'Location manager failed: {str(e)}'
            }
        
        # Test location service
        try:
            service = LocationService()
            checks['location_service'] = {
                'status': 'healthy',
                'message': 'Location service instantiated successfully'
            }
        except Exception as e:
            checks['location_service'] = {
                'status': 'critical',
                'message': f'Location service failed: {str(e)}'
            }
        
        return checks
        
    except ImportError as e:
        return {
            'import_error': {
                'status': 'critical',
                'message': f'Failed to import location service: {str(e)}'
            }
        }

def export_service_health_check() -> Dict[str, Any]:
    """Health check for export service."""
    try:
        from .export_service import ExportService
        import pandas as pd
        
        checks = {}
        
        # Test export service
        try:
            service = ExportService()
            checks['export_service'] = {
                'status': 'healthy',
                'message': 'Export service instantiated successfully'
            }
        except Exception as e:
            checks['export_service'] = {
                'status': 'critical',
                'message': f'Export service failed: {str(e)}'
            }
        
        # Test pandas dependency
        try:
            df = pd.DataFrame({'test': [1, 2, 3]})
            checks['pandas_dependency'] = {
                'status': 'healthy',
                'message': 'Pandas working correctly',
                'details': {'version': pd.__version__}
            }
        except Exception as e:
            checks['pandas_dependency'] = {
                'status': 'critical',
                'message': f'Pandas failed: {str(e)}'
            }
        
        return checks
        
    except ImportError as e:
        return {
            'import_error': {
                'status': 'critical',
                'message': f'Failed to import export service: {str(e)}'
            }
        }

# Global health monitor instance
global_health_monitor = HealthMonitor()

def setup_health_monitoring():
    """Setup health monitoring for all services."""
    global_health_monitor.register_service('financial_service', financial_service_health_check)
    global_health_monitor.register_service('location_service', location_service_health_check)
    global_health_monitor.register_service('export_service', export_service_health_check)
    
    logging.info("Health monitoring setup completed for all services")

def get_health_status() -> Dict[str, Any]:
    """Get current health status of all services."""
    return global_health_monitor.get_system_health_summary()
