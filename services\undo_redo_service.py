"""
Undo/Redo Service
=================

Comprehensive undo/redo functionality using the Command pattern with
state management, history tracking, and event callbacks.
"""

import copy
import logging
import threading
from typing import Any, Dict, List, Optional, Callable, Protocol, TypeVar, Generic
from datetime import datetime
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
from enum import Enum


class CommandType(Enum):
    """Types of commands that can be undone/redone."""
    STATE_CHANGE = "state_change"
    PROPERTY_UPDATE = "property_update"
    DATA_MODIFICATION = "data_modification"
    UI_ACTION = "ui_action"
    CALCULATION = "calculation"


@dataclass
class CommandMetadata:
    """Metadata for command tracking."""
    command_id: str
    command_type: CommandType
    timestamp: datetime
    description: str
    user_action: str = ""
    affected_objects: List[str] = field(default_factory=list)
    size_bytes: int = 0


class UndoableCommand(ABC):
    """Abstract base class for undoable commands."""
    
    def __init__(self, description: str, command_type: CommandType = CommandType.STATE_CHANGE):
        self.metadata = CommandMetadata(
            command_id=self._generate_id(),
            command_type=command_type,
            timestamp=datetime.now(),
            description=description
        )
        self.executed = False
    
    @abstractmethod
    def execute(self) -> Any:
        """Execute the command."""
        pass
    
    @abstractmethod
    def undo(self) -> Any:
        """Undo the command."""
        pass
    
    def redo(self) -> Any:
        """Redo the command (default implementation re-executes)."""
        return self.execute()
    
    def can_merge_with(self, other: 'UndoableCommand') -> bool:
        """Check if this command can be merged with another."""
        return False
    
    def merge_with(self, other: 'UndoableCommand') -> 'UndoableCommand':
        """Merge this command with another (if supported)."""
        raise NotImplementedError("Command merging not supported")
    
    def get_size_estimate(self) -> int:
        """Estimate memory size of the command."""
        return 1000  # Default estimate in bytes
    
    def _generate_id(self) -> str:
        """Generate unique command ID."""
        import uuid
        return str(uuid.uuid4())[:8]


class StateChangeCommand(UndoableCommand):
    """Command for tracking state changes in objects."""
    
    def __init__(self, target: Any, property_name: str, new_value: Any, 
                 old_value: Any = None, description: str = None):
        self.target = target
        self.property_name = property_name
        self.new_value = copy.deepcopy(new_value)
        self.old_value = copy.deepcopy(old_value) if old_value is not None else self._get_current_value()
        
        desc = description or f"Change {property_name}"
        super().__init__(desc, CommandType.PROPERTY_UPDATE)
        
        self.metadata.affected_objects = [str(id(target))]
        self.metadata.size_bytes = self._estimate_size()
    
    def execute(self) -> Any:
        """Execute the state change."""
        if hasattr(self.target, self.property_name):
            setattr(self.target, self.property_name, self.new_value)
            self.executed = True
            return self.new_value
        else:
            raise AttributeError(f"Object has no attribute '{self.property_name}'")
    
    def undo(self) -> Any:
        """Undo the state change."""
        if hasattr(self.target, self.property_name):
            setattr(self.target, self.property_name, self.old_value)
            return self.old_value
        else:
            raise AttributeError(f"Object has no attribute '{self.property_name}'")
    
    def redo(self) -> Any:
        """Redo the state change."""
        return self.execute()
    
    def can_merge_with(self, other: 'UndoableCommand') -> bool:
        """Check if can merge with another StateChangeCommand."""
        if not isinstance(other, StateChangeCommand):
            return False
        
        return (self.target is other.target and 
                self.property_name == other.property_name and
                (other.metadata.timestamp - self.metadata.timestamp).seconds < 5)
    
    def merge_with(self, other: 'StateChangeCommand') -> 'StateChangeCommand':
        """Merge with another state change command."""
        if not self.can_merge_with(other):
            raise ValueError("Commands cannot be merged")
        
        # Create new merged command
        merged = StateChangeCommand(
            target=self.target,
            property_name=self.property_name,
            new_value=other.new_value,
            old_value=self.old_value,
            description=f"Change {self.property_name} (merged)"
        )
        
        merged.metadata.timestamp = other.metadata.timestamp
        return merged
    
    def _get_current_value(self) -> Any:
        """Get current value of the property."""
        if hasattr(self.target, self.property_name):
            return getattr(self.target, self.property_name)
        return None
    
    def _estimate_size(self) -> int:
        """Estimate memory size of stored values."""
        try:
            import sys
            old_size = sys.getsizeof(self.old_value) if self.old_value is not None else 0
            new_size = sys.getsizeof(self.new_value) if self.new_value is not None else 0
            return old_size + new_size + 200  # Base overhead
        except:
            return 1000  # Fallback estimate


class CompoundCommand(UndoableCommand):
    """Command that contains multiple sub-commands."""
    
    def __init__(self, commands: List[UndoableCommand], description: str):
        super().__init__(description, CommandType.DATA_MODIFICATION)
        self.commands = commands
        self.metadata.affected_objects = []
        
        # Aggregate metadata from sub-commands
        for cmd in commands:
            self.metadata.affected_objects.extend(cmd.metadata.affected_objects)
        
        self.metadata.size_bytes = sum(cmd.get_size_estimate() for cmd in commands)
    
    def execute(self) -> List[Any]:
        """Execute all sub-commands."""
        results = []
        for cmd in self.commands:
            try:
                result = cmd.execute()
                results.append(result)
            except Exception as e:
                # Rollback executed commands
                for executed_cmd in reversed(self.commands[:len(results)]):
                    try:
                        executed_cmd.undo()
                    except:
                        pass
                raise e
        
        self.executed = True
        return results
    
    def undo(self) -> List[Any]:
        """Undo all sub-commands in reverse order."""
        results = []
        for cmd in reversed(self.commands):
            try:
                result = cmd.undo()
                results.append(result)
            except Exception as e:
                # Continue with other commands
                results.append(None)
        
        return results
    
    def redo(self) -> List[Any]:
        """Redo all sub-commands."""
        return self.execute()


class UndoRedoService:
    """Comprehensive undo/redo service with history management."""
    
    def __init__(self, max_history_size: int = 100, max_memory_mb: int = 50):
        self.max_history_size = max_history_size
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        
        self.undo_stack: List[UndoableCommand] = []
        self.redo_stack: List[UndoableCommand] = []
        self.lock = threading.RLock()
        
        self.logger = logging.getLogger(__name__)
        
        # Event callbacks
        self.on_command_executed: Optional[Callable[[UndoableCommand], None]] = None
        self.on_undo_performed: Optional[Callable[[UndoableCommand], None]] = None
        self.on_redo_performed: Optional[Callable[[UndoableCommand], None]] = None
        self.on_history_changed: Optional[Callable[[], None]] = None
        
        # Statistics
        self.stats = {
            'commands_executed': 0,
            'undos_performed': 0,
            'redos_performed': 0,
            'commands_merged': 0,
            'memory_cleanups': 0
        }
        
        self.logger.info("Undo/Redo service initialized")
    
    def execute_command(self, command: UndoableCommand) -> Any:
        """Execute a command and add it to the undo stack."""
        with self.lock:
            try:
                # Check if we can merge with the last command
                if self.undo_stack and self._should_merge_commands(self.undo_stack[-1], command):
                    merged_command = self.undo_stack[-1].merge_with(command)
                    result = merged_command.execute()
                    self.undo_stack[-1] = merged_command
                    self.stats['commands_merged'] += 1
                    self.logger.debug(f"Merged command: {merged_command.metadata.description}")
                else:
                    # Execute the command
                    result = command.execute()
                    
                    # Add to undo stack
                    self.undo_stack.append(command)
                    
                    # Clear redo stack (new action invalidates redo history)
                    self.redo_stack.clear()
                    
                    # Manage history size and memory
                    self._manage_history_size()
                    self._manage_memory_usage()
                
                self.stats['commands_executed'] += 1
                
                # Trigger callbacks
                if self.on_command_executed:
                    self.on_command_executed(command)
                if self.on_history_changed:
                    self.on_history_changed()
                
                self.logger.debug(f"Executed command: {command.metadata.description}")
                return result
                
            except Exception as e:
                self.logger.error(f"Failed to execute command {command.metadata.description}: {e}")
                raise
    
    def undo(self) -> Optional[Any]:
        """Undo the last command."""
        with self.lock:
            if not self.undo_stack:
                self.logger.warning("No commands to undo")
                return None
            
            command = self.undo_stack.pop()
            
            try:
                result = command.undo()
                self.redo_stack.append(command)
                
                self.stats['undos_performed'] += 1
                
                # Trigger callbacks
                if self.on_undo_performed:
                    self.on_undo_performed(command)
                if self.on_history_changed:
                    self.on_history_changed()
                
                self.logger.debug(f"Undid command: {command.metadata.description}")
                return result
                
            except Exception as e:
                # Put command back if undo failed
                self.undo_stack.append(command)
                self.logger.error(f"Failed to undo command {command.metadata.description}: {e}")
                raise
    
    def redo(self) -> Optional[Any]:
        """Redo the last undone command."""
        with self.lock:
            if not self.redo_stack:
                self.logger.warning("No commands to redo")
                return None
            
            command = self.redo_stack.pop()
            
            try:
                result = command.redo()
                self.undo_stack.append(command)
                
                self.stats['redos_performed'] += 1
                
                # Trigger callbacks
                if self.on_redo_performed:
                    self.on_redo_performed(command)
                if self.on_history_changed:
                    self.on_history_changed()
                
                self.logger.debug(f"Redid command: {command.metadata.description}")
                return result
                
            except Exception as e:
                # Put command back if redo failed
                self.redo_stack.append(command)
                self.logger.error(f"Failed to redo command {command.metadata.description}: {e}")
                raise
    
    def can_undo(self) -> bool:
        """Check if undo is possible."""
        with self.lock:
            return len(self.undo_stack) > 0
    
    def can_redo(self) -> bool:
        """Check if redo is possible."""
        with self.lock:
            return len(self.redo_stack) > 0
    
    def get_undo_description(self) -> Optional[str]:
        """Get description of next undo command."""
        with self.lock:
            if self.undo_stack:
                return self.undo_stack[-1].metadata.description
            return None
    
    def get_redo_description(self) -> Optional[str]:
        """Get description of next redo command."""
        with self.lock:
            if self.redo_stack:
                return self.redo_stack[-1].metadata.description
            return None
    
    def get_history_summary(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Get summary of command history."""
        with self.lock:
            undo_commands = [
                {
                    'id': cmd.metadata.command_id,
                    'description': cmd.metadata.description,
                    'type': cmd.metadata.command_type.value,
                    'timestamp': cmd.metadata.timestamp.isoformat(),
                    'can_undo': True,
                    'can_redo': False
                }
                for cmd in self.undo_stack[-limit:]
            ]
            
            redo_commands = [
                {
                    'id': cmd.metadata.command_id,
                    'description': cmd.metadata.description,
                    'type': cmd.metadata.command_type.value,
                    'timestamp': cmd.metadata.timestamp.isoformat(),
                    'can_undo': False,
                    'can_redo': True
                }
                for cmd in reversed(self.redo_stack[-limit:])
            ]
            
            return undo_commands + redo_commands
    
    def clear_history(self):
        """Clear all undo/redo history."""
        with self.lock:
            self.undo_stack.clear()
            self.redo_stack.clear()
            
            if self.on_history_changed:
                self.on_history_changed()
            
            self.logger.info("Cleared undo/redo history")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get service statistics."""
        with self.lock:
            current_memory = sum(cmd.get_size_estimate() for cmd in self.undo_stack + self.redo_stack)
            
            return {
                **self.stats,
                'undo_stack_size': len(self.undo_stack),
                'redo_stack_size': len(self.redo_stack),
                'current_memory_bytes': current_memory,
                'memory_limit_bytes': self.max_memory_bytes,
                'history_limit': self.max_history_size
            }
    
    def create_state_change_command(self, target: Any, property_name: str, 
                                   new_value: Any, description: str = None) -> StateChangeCommand:
        """Factory method for creating state change commands."""
        return StateChangeCommand(target, property_name, new_value, description=description)
    
    def create_compound_command(self, commands: List[UndoableCommand], 
                               description: str) -> CompoundCommand:
        """Factory method for creating compound commands."""
        return CompoundCommand(commands, description)
    
    def _should_merge_commands(self, last_command: UndoableCommand, 
                              new_command: UndoableCommand) -> bool:
        """Check if commands should be merged."""
        return (last_command.can_merge_with(new_command) and
                (new_command.metadata.timestamp - last_command.metadata.timestamp).seconds < 5)
    
    def _manage_history_size(self):
        """Manage history size to stay within limits."""
        while len(self.undo_stack) > self.max_history_size:
            removed = self.undo_stack.pop(0)
            self.logger.debug(f"Removed old command from history: {removed.metadata.description}")
    
    def _manage_memory_usage(self):
        """Manage memory usage by removing old commands if needed."""
        current_memory = sum(cmd.get_size_estimate() for cmd in self.undo_stack + self.redo_stack)
        
        while current_memory > self.max_memory_bytes and (self.undo_stack or self.redo_stack):
            # Remove oldest command
            if self.undo_stack:
                removed = self.undo_stack.pop(0)
            elif self.redo_stack:
                removed = self.redo_stack.pop(0)
            else:
                break
            
            current_memory -= removed.get_size_estimate()
            self.stats['memory_cleanups'] += 1
            self.logger.debug(f"Removed command for memory management: {removed.metadata.description}")


# Context manager for grouped commands
class CommandGroup:
    """Context manager for grouping multiple commands into a compound command."""
    
    def __init__(self, service: UndoRedoService, description: str):
        self.service = service
        self.description = description
        self.commands: List[UndoableCommand] = []
        self.original_execute = None
    
    def __enter__(self):
        # Temporarily intercept command execution
        self.original_execute = self.service.execute_command
        self.service.execute_command = self._collect_command
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        # Restore original execute method
        self.service.execute_command = self.original_execute
        
        # Execute compound command if no exception and we have commands
        if exc_type is None and self.commands:
            compound_command = CompoundCommand(self.commands, self.description)
            self.original_execute(compound_command)
    
    def _collect_command(self, command: UndoableCommand):
        """Collect commands instead of executing them immediately."""
        self.commands.append(command)
        return command.execute()


# Global service instance
_undo_redo_service: Optional[UndoRedoService] = None

def get_undo_redo_service() -> UndoRedoService:
    """Get global undo/redo service instance."""
    global _undo_redo_service
    if _undo_redo_service is None:
        _undo_redo_service = UndoRedoService()
    return _undo_redo_service


def initialize_undo_redo_service(max_history_size: int = 100, 
                                max_memory_mb: int = 50) -> UndoRedoService:
    """Initialize global undo/redo service with custom settings."""
    global _undo_redo_service
    _undo_redo_service = UndoRedoService(max_history_size, max_memory_mb)
    return _undo_redo_service


# Convenience functions
def execute_command(command: UndoableCommand) -> Any:
    """Execute a command using the global service."""
    return get_undo_redo_service().execute_command(command)


def undo() -> Optional[Any]:
    """Undo the last command using the global service."""
    return get_undo_redo_service().undo()


def redo() -> Optional[Any]:
    """Redo the last undone command using the global service."""
    return get_undo_redo_service().redo()


def create_command_group(description: str) -> CommandGroup:
    """Create a command group context manager."""
    return CommandGroup(get_undo_redo_service(), description)