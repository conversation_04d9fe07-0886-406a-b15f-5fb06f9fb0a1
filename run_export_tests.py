#!/usr/bin/env python3
"""
Export Functionality Test Runner
===============================

Script to run comprehensive tests for the export functionality fixes.
This verifies that charts, DCF tables, and detailed analysis are properly
included in all export formats.
"""

import sys
import unittest
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def run_export_tests():
    """Run all export functionality tests."""
    print("=" * 60)
    print("EXPORT FUNCTIONALITY TEST SUITE")
    print("=" * 60)
    print()
    
    # Import test modules
    try:
        from tests.test_export_functionality import TestExportFunctionality
        print("✓ Test modules imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import test modules: {e}")
        return False
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add all test methods
    test_methods = [
        'test_output_directory_creation',
        'test_chart_generation_and_export',
        'test_excel_export_with_dcf_tables',
        'test_docx_export_with_charts',
        'test_html_export_with_embedded_charts',
        'test_json_export_with_complete_data',
        'test_comprehensive_report_generation',
        'test_export_configuration_settings',
        'test_file_utils_verification',
        'test_error_handling_and_recovery'
    ]
    
    for method_name in test_methods:
        test_suite.addTest(TestExportFunctionality(method_name))
    
    print(f"✓ Test suite created with {len(test_methods)} tests")
    print()
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
    result = runner.run(test_suite)
    
    print()
    print("=" * 60)
    print("TEST RESULTS SUMMARY")
    print("=" * 60)
    
    total_tests = result.testsRun
    failures = len(result.failures)
    errors = len(result.errors)
    skipped = len(result.skipped) if hasattr(result, 'skipped') else 0
    passed = total_tests - failures - errors - skipped
    
    print(f"Total Tests:  {total_tests}")
    print(f"Passed:       {passed}")
    print(f"Failed:       {failures}")
    print(f"Errors:       {errors}")
    print(f"Skipped:      {skipped}")
    print()
    
    if failures > 0:
        print("FAILURES:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip()}")
        print()
    
    if errors > 0:
        print("ERRORS:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('Exception:')[-1].strip()}")
        print()
    
    success_rate = (passed / total_tests) * 100 if total_tests > 0 else 0
    print(f"Success Rate: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("🎉 EXCELLENT! Export functionality is working well.")
    elif success_rate >= 75:
        print("✅ GOOD! Most export functionality is working.")
    elif success_rate >= 50:
        print("⚠️  NEEDS IMPROVEMENT! Some export functionality issues detected.")
    else:
        print("❌ CRITICAL! Major export functionality problems detected.")
    
    print()
    print("=" * 60)
    
    return success_rate >= 75

def run_quick_validation():
    """Run a quick validation of key export components."""
    print("QUICK VALIDATION")
    print("-" * 20)
    
    validation_results = {}
    
    # Test 1: Import all required modules
    try:
        from services.export_service import ExportService
        from services.report_service import ReportGenerationService
        from components.charts.chart_factory import ChartFactory
        from utils.file_utils import FileUtils
        from config.export_config import ExportConfig
        validation_results['imports'] = True
        print("✓ All required modules can be imported")
    except Exception as e:
        validation_results['imports'] = False
        print(f"✗ Module import failed: {e}")
    
    # Test 2: Create service instances
    try:
        export_service = ExportService()
        report_service = ReportGenerationService()
        chart_factory = ChartFactory()
        file_utils = FileUtils()
        export_config = ExportConfig()
        validation_results['instantiation'] = True
        print("✓ All service instances created successfully")
    except Exception as e:
        validation_results['instantiation'] = False
        print(f"✗ Service instantiation failed: {e}")
    
    # Test 3: Check configuration
    try:
        chart_settings = export_config.get_chart_settings()
        color_palette = export_config.get_color_palette()
        validation_results['configuration'] = True
        print("✓ Export configuration is accessible")
    except Exception as e:
        validation_results['configuration'] = False
        print(f"✗ Configuration access failed: {e}")
    
    # Test 4: Check chart factory methods
    try:
        # Test that chart factory has required methods
        required_methods = [
            'create_and_export_bar_chart',
            'create_and_export_line_chart',
            'create_dcf_waterfall_chart'
        ]
        for method_name in required_methods:
            if not hasattr(chart_factory, method_name):
                raise AttributeError(f"Missing method: {method_name}")
        validation_results['chart_methods'] = True
        print("✓ Chart factory has all required methods")
    except Exception as e:
        validation_results['chart_methods'] = False
        print(f"✗ Chart factory validation failed: {e}")
    
    # Test 5: Check export service methods
    try:
        required_methods = [
            'export_excel_report',
            'export_docx_report',
            'export_html_report',
            'export_json_data'
        ]
        for method_name in required_methods:
            if not hasattr(export_service, method_name):
                raise AttributeError(f"Missing method: {method_name}")
        validation_results['export_methods'] = True
        print("✓ Export service has all required methods")
    except Exception as e:
        validation_results['export_methods'] = False
        print(f"✗ Export service validation failed: {e}")
    
    print()
    passed_validations = sum(validation_results.values())
    total_validations = len(validation_results)
    
    print(f"Quick Validation: {passed_validations}/{total_validations} checks passed")
    
    return passed_validations == total_validations

if __name__ == '__main__':
    print("Starting Export Functionality Tests...")
    print()
    
    # Run quick validation first
    if not run_quick_validation():
        print("❌ Quick validation failed. Please fix basic issues before running full tests.")
        sys.exit(1)
    
    print()
    
    # Run full test suite
    success = run_export_tests()
    
    if success:
        print("🎉 Export functionality tests completed successfully!")
        sys.exit(0)
    else:
        print("❌ Export functionality tests failed. Please review and fix issues.")
        sys.exit(1)
