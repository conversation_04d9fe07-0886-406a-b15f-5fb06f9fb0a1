#!/usr/bin/env python3
"""
Build Script for Hiel Renewable Energy Financial Modeler
========================================================

This script builds a standalone executable (.exe) from the Flet application.
Supports both flet build (recommended) and PyInstaller as fallback.

Usage:
    python build_exe.py [--pyinstaller] [--debug]
"""

import os
import sys
import subprocess
import shutil
import argparse
from pathlib import Path
import json

# Application Configuration
APP_NAME = "Hiel Renewable Energy Financial Modeler"
APP_SHORT_NAME = "HielRnEModeler"
APP_VERSION = "3.0.0"
APP_DESCRIPTION = "Professional Financial Modeling Tool for Renewable Energy Projects"
APP_COPYRIGHT = "© 2025 Hiel RnE Solutions"
MAIN_FILE = "main.py"
ICON_FILE = "assets/logo.ico"  # Place your logo here as .ico format

class BuildConfig:
    """Build configuration and utilities."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.dist_dir = self.project_root / "dist"
        self.build_dir = self.project_root / "build"
        self.assets_dir = self.project_root / "assets"
        
    def setup_directories(self):
        """Create necessary directories."""
        self.assets_dir.mkdir(exist_ok=True)
        print(f"✓ Assets directory ready: {self.assets_dir}")
        
        if not (self.assets_dir / "logo.ico").exists():
            print(f"⚠️  Icon file not found: {self.assets_dir / 'logo.ico'}")
            print("   Place your logo as 'assets/logo.ico' for app icon")
    
    def clean_build(self):
        """Clean previous build artifacts."""
        if self.dist_dir.exists():
            shutil.rmtree(self.dist_dir)
            print("✓ Cleaned dist directory")
        
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
            print("✓ Cleaned build directory")
    
    def check_dependencies(self):
        """Check if required dependencies are installed."""
        try:
            import flet
            print(f"✓ Flet version: {flet.__version__}")
        except ImportError:
            print("❌ Flet not installed. Run: pip install flet")
            return False
        
        # Check other critical dependencies
        required_packages = [
            'numpy', 'pandas', 'numpy_financial'
        ]
        
        missing = []
        for package in required_packages:
            try:
                __import__(package)
                print(f"✓ {package} installed")
            except ImportError:
                missing.append(package)
        
        if missing:
            print(f"❌ Missing packages: {', '.join(missing)}")
            print(f"   Run: pip install {' '.join(missing)}")
            return False
        
        return True

def build_with_flet(debug=False):
    """Build using Flet's built-in build system (recommended)."""
    print("\n🔨 Building with Flet...")
    
    # Prepare flet build command
    cmd = [
        sys.executable, "-m", "flet", "build", "windows",
        "--name", APP_SHORT_NAME,
        "--description", APP_DESCRIPTION,
        "--product", APP_NAME,
        "--version", APP_VERSION,
        "--copyright", APP_COPYRIGHT,
        "--onefile",
        "--no-console"  # Remove console window for GUI app
    ]
    
    # Add icon if available
    icon_path = Path("assets/logo.ico")
    if icon_path.exists():
        cmd.extend(["--icon", str(icon_path)])
        print(f"✓ Using icon: {icon_path}")
    else:
        print("⚠️  No icon specified - using default")
    
    # Add debug flag if requested
    if debug:
        cmd.append("--verbose")
    
    # Add main file
    cmd.append(MAIN_FILE)
    
    print(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ Flet build completed successfully!")
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Flet build failed: {e}")
        print(f"Error output: {e.stderr}")
        return False
    except FileNotFoundError:
        print("❌ Flet build command not found. Ensure Flet is properly installed.")
        return False

def build_with_pyinstaller(debug=False):
    """Build using PyInstaller as fallback."""
    print("\n🔨 Building with PyInstaller (fallback)...")
    
    try:
        import PyInstaller
        print(f"✓ PyInstaller version: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller not installed. Run: pip install pyinstaller")
        return False
    
    # PyInstaller command
    cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed",  # No console window
        "--name", APP_SHORT_NAME,
        f"--distpath={Path('dist').absolute()}",
        f"--workpath={Path('build').absolute()}",
    ]
    
    # Add icon if available
    icon_path = Path("assets/logo.ico")
    if icon_path.exists():
        cmd.extend(["--icon", str(icon_path.absolute())])
    
    # Add hidden imports for common issues
    hidden_imports = [
        "flet",
        "numpy",
        "pandas",
        "numpy_financial",
        "sqlite3",
        "logging",
        "json",
        "datetime",
        "dataclasses",
        "typing"
    ]
    
    for imp in hidden_imports:
        cmd.extend(["--hidden-import", imp])
    
    # Add data files
    data_files = [
        ("config", "config"),
        ("templates", "templates"),
    ]
    
    for src, dst in data_files:
        if Path(src).exists():
            cmd.extend(["--add-data", f"{src};{dst}"])
    
    if debug:
        cmd.append("--debug=all")
    
    cmd.append(MAIN_FILE)
    
    print(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True)
        print("✓ PyInstaller build completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ PyInstaller build failed: {e}")
        return False

def create_version_info():
    """Create version info file for Windows executable."""
    version_info = f"""# UTF-8
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=({APP_VERSION.replace('.', ', ')}, 0),
    prodvers=({APP_VERSION.replace('.', ', ')}, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo([
      StringTable(u'040904B0', [
        StringStruct(u'CompanyName', u'Hiel RnE Solutions'),
        StringStruct(u'FileDescription', u'{APP_DESCRIPTION}'),
        StringStruct(u'FileVersion', u'{APP_VERSION}'),
        StringStruct(u'InternalName', u'{APP_SHORT_NAME}'),
        StringStruct(u'LegalCopyright', u'{APP_COPYRIGHT}'),
        StringStruct(u'OriginalFilename', u'{APP_SHORT_NAME}.exe'),
        StringStruct(u'ProductName', u'{APP_NAME}'),
        StringStruct(u'ProductVersion', u'{APP_VERSION}')
      ])
    ]),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
"""
    
    with open("version_info.txt", "w", encoding="utf-8") as f:
        f.write(version_info)
    
    print("✓ Created version info file")

def create_build_info():
    """Create build information file."""
    build_info = {
        "app_name": APP_NAME,
        "app_short_name": APP_SHORT_NAME,
        "version": APP_VERSION,
        "description": APP_DESCRIPTION,
        "copyright": APP_COPYRIGHT,
        "build_date": str(Path(__file__).stat().st_mtime),
        "python_version": sys.version,
        "platform": sys.platform
    }
    
    with open("dist/build_info.json", "w") as f:
        json.dump(build_info, f, indent=2)
    
    print("✓ Created build info file")

def main():
    """Main build function."""
    parser = argparse.ArgumentParser(description="Build Hiel RnE Financial Modeler executable")
    parser.add_argument("--pyinstaller", action="store_true", help="Use PyInstaller instead of Flet build")
    parser.add_argument("--debug", action="store_true", help="Enable debug output")
    parser.add_argument("--clean", action="store_true", help="Clean build directories first")
    
    args = parser.parse_args()
    
    print(f"""
🏗️  Building {APP_NAME} v{APP_VERSION}
{'='*60}
""")
    
    # Initialize build configuration
    config = BuildConfig()
    config.setup_directories()
    
    if args.clean:
        config.clean_build()
    
    # Check dependencies
    if not config.check_dependencies():
        print("\n❌ Dependency check failed. Please install missing packages.")
        return 1
    
    # Check main file exists
    if not Path(MAIN_FILE).exists():
        print(f"❌ Main file not found: {MAIN_FILE}")
        return 1
    
    # Create version info
    create_version_info()
    
    # Build the application
    success = False
    
    if args.pyinstaller:
        success = build_with_pyinstaller(args.debug)
    else:
        success = build_with_flet(args.debug)
        
        # Fallback to PyInstaller if Flet build fails
        if not success:
            print("\n🔄 Flet build failed, trying PyInstaller...")
            success = build_with_pyinstaller(args.debug)
    
    if success:
        # Create build info
        Path("dist").mkdir(exist_ok=True)
        create_build_info()
        
        print(f"""
✅ Build completed successfully!

📦 Executable location: dist/{APP_SHORT_NAME}.exe
📊 Application: {APP_NAME} v{APP_VERSION}
💼 Description: {APP_DESCRIPTION}

🚀 You can now distribute the executable file!
""")
        
        # Show file size
        exe_path = Path(f"dist/{APP_SHORT_NAME}.exe")
        if exe_path.exists():
            size_mb = exe_path.stat().st_size / (1024 * 1024)
            print(f"📏 File size: {size_mb:.1f} MB")
        
        return 0
    else:
        print("\n❌ Build failed! Check the error messages above.")
        return 1

if __name__ == "__main__":
    exit(main()) 