"""
Comparison Charts
=================

Chart components for location and scenario comparisons.
"""

import flet as ft
from typing import Dict, Any, Optional, List
import pandas as pd

from components.charts.chart_factory import ChartFactory


class ComparisonCharts:
    """Chart components for comparison analysis."""
    
    def __init__(self):
        self.chart_factory = ChartFactory()
    
    def create_location_comparison_chart(self, comparison_results: Dict[str, Any]) -> ft.Container:
        """Create location comparison chart."""
        if not comparison_results:
            return ft.Container()
        
        analysis = comparison_results.get('analysis', {})
        comparison_matrix = analysis.get('comparison_matrix', [])
        
        if not comparison_matrix:
            return ft.Container(
                content=ft.Text("No comparison data available"),
                alignment=ft.alignment.center
            )
        
        # Extract data for chart
        locations = [item['Location'] for item in comparison_matrix]
        irr_values = [item['IRR_Project'] * 100 for item in comparison_matrix]
        
        irr_data = dict(zip(locations, irr_values))
        
        return self.chart_factory.create_bar_chart(
            irr_data,
            "Project IRR by Location",
            "Location",
            "IRR (%)"
        )
    
    def create_location_ranking_chart(self, comparison_results: Dict[str, Any]) -> ft.Container:
        """Create location ranking visualization."""
        if not comparison_results:
            return ft.Container()
        
        analysis = comparison_results.get('analysis', {})
        rankings = analysis.get('rankings', {})
        
        # Create ranking display for multiple metrics
        ranking_content = ft.Column([
            ft.Text("Location Rankings", 
                   size=16, weight=ft.FontWeight.BOLD,
                   text_align=ft.TextAlign.CENTER),
            ft.Divider(height=10)
        ])
        
        metrics = [
            ("Best Project IRR", rankings.get('best_irr_project', [])),
            ("Best Equity IRR", rankings.get('best_irr_equity', [])),
            ("Lowest LCOE", rankings.get('lowest_lcoe', []))
        ]
        
        for metric_name, ranking_data in metrics:
            if ranking_data:
                metric_content = ft.Column([
                    ft.Text(metric_name, size=14, weight=ft.FontWeight.BOLD),
                    *[
                        ft.Row([
                            ft.Container(
                                content=ft.Text(str(item['rank']), 
                                               color=ft.Colors.WHITE, 
                                               weight=ft.FontWeight.BOLD),
                                width=25,
                                height=25,
                                bgcolor=self._get_rank_color(item['rank']),
                                border_radius=12,
                                alignment=ft.alignment.center
                            ),
                            ft.Text(item['location'], expand=1),
                            ft.Text(f"{item['value']:.3f}" if isinstance(item['value'], float) else str(item['value']))
                        ]) for item in ranking_data[:3]  # Top 3
                    ]
                ])
                ranking_content.controls.append(metric_content)
                ranking_content.controls.append(ft.Divider(height=10))
        
        return ft.Container(
            content=ranking_content,
            padding=15,
            bgcolor=ft.Colors.WHITE,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.GREY_300)
        )
    
    def _get_rank_color(self, rank: int) -> str:
        """Get color based on ranking."""
        if rank == 1:
            return ft.Colors.GOLD
        elif rank == 2:
            return ft.Colors.GREY_400  # Silver
        elif rank == 3:
            return ft.Colors.BROWN  # Bronze
        else:
            return ft.Colors.GREY_600
    
    def create_scenario_comparison_chart(self, scenario_results: Dict[str, Any]) -> ft.Container:
        """Create scenario comparison chart."""
        if not scenario_results:
            return ft.Container()
        
        # Extract scenario data
        scenario_data = {}
        for scenario_name, results in scenario_results.items():
            if 'kpis' in results:
                irr = results['kpis'].get('IRR_project', 0) * 100
                scenario_data[scenario_name] = irr
        
        if not scenario_data:
            return ft.Container(
                content=ft.Text("No scenario data available"),
                alignment=ft.alignment.center
            )
        
        return self.chart_factory.create_bar_chart(
            scenario_data,
            "Project IRR by Scenario",
            "Scenario",
            "IRR (%)"
        )
    
    def create_sensitivity_tornado_chart(self, sensitivity_results: pd.DataFrame) -> ft.Container:
        """Create tornado chart for sensitivity analysis."""
        if sensitivity_results is None or sensitivity_results.empty:
            return ft.Container(
                content=ft.Text("No sensitivity data available"),
                alignment=ft.alignment.center
            )
        
        # Placeholder for tornado chart
        return ft.Container(
            content=ft.Text("Sensitivity tornado chart would be displayed here",
                          text_align=ft.TextAlign.CENTER),
            height=300,
            alignment=ft.alignment.center,
            bgcolor=ft.Colors.GREY_100,
            border_radius=8
        )
    
    def create_monte_carlo_distribution_chart(self, monte_carlo_results: Dict[str, Any]) -> ft.Container:
        """Create Monte Carlo distribution chart."""
        if not monte_carlo_results:
            return ft.Container()
        
        # Placeholder for Monte Carlo distribution
        return ft.Container(
            content=ft.Text("Monte Carlo distribution chart would be displayed here",
                          text_align=ft.TextAlign.CENTER),
            height=300,
            alignment=ft.alignment.center,
            bgcolor=ft.Colors.GREY_100,
            border_radius=8
        )
    
    def create_comparison_matrix_heatmap(self, comparison_data: List[Dict[str, Any]]) -> ft.Container:
        """Create comparison matrix heatmap."""
        if not comparison_data:
            return ft.Container()
        
        # Placeholder for heatmap
        return ft.Container(
            content=ft.Text("Comparison matrix heatmap would be displayed here",
                          text_align=ft.TextAlign.CENTER),
            height=300,
            alignment=ft.alignment.center,
            bgcolor=ft.Colors.GREY_100,
            border_radius=8
        )
