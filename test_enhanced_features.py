"""
Test Enhanced Features
======================

Test script to verify all enhanced features are working correctly.
"""

import logging
import sys
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_persistence_service():
    """Test data persistence service."""
    logger.info("Testing Data Persistence Service...")
    
    try:
        from services.persistence_service import DataPersistenceService, ProjectData
        
        # Initialize service
        service = DataPersistenceService()
        
        # Create test project
        test_project = ProjectData(
            id="test_project_001",
            name="Test Solar Project",
            client_profile={"name": "Test Client", "industry": "Solar"},
            project_assumptions={"capacity_mw": 10, "capex_meur": 8.5},
            financial_results={"irr": 0.12, "npv": 1000000},
            tags=["test", "solar"],
            description="Test project for enhanced features"
        )
        
        # Test save
        success = service.save_project(test_project)
        assert success, "Failed to save project"
        logger.info("✓ Project saved successfully")
        
        # Test save_project_version
        version_id = service.save_project_version(
            "test_project_001",
            {
                "name": "Test Solar Project",
                "client_profile": test_project.client_profile,
                "project_assumptions": test_project.project_assumptions,
                "financial_results": test_project.financial_results
            },
            "Test version"
        )
        assert version_id, "Failed to save project version"
        logger.info(f"✓ Project version saved: {version_id}")
        
        # Test load
        loaded_project = service.load_project("test_project_001")
        assert loaded_project is not None, "Failed to load project"
        assert loaded_project.name == test_project.name, "Project data mismatch"
        logger.info("✓ Project loaded successfully")
        
        # Test list projects
        projects = service.list_projects()
        assert len(projects) > 0, "No projects found"
        logger.info(f"✓ Listed {len(projects)} projects")
        
        # Test get versions
        versions = service.get_project_versions("test_project_001")
        assert len(versions) > 0, "No versions found"
        logger.info(f"✓ Found {len(versions)} versions")
        
        # Cleanup
        service.delete_project("test_project_001", soft_delete=False)
        
        logger.info("✅ Data Persistence Service: PASSED\n")
        return True
        
    except Exception as e:
        logger.error(f"❌ Data Persistence Service: FAILED - {e}\n")
        return False


def test_undo_redo_service():
    """Test undo/redo service."""
    logger.info("Testing Undo/Redo Service...")
    
    try:
        from services.undo_redo_service import UndoRedoService, StateChangeCommand
        
        # Initialize service
        service = UndoRedoService()
        
        # Test object for state changes
        class TestObject:
            def __init__(self):
                self.value = 0
                self.name = "test"
        
        obj = TestObject()
        
        # Create and execute command
        command = StateChangeCommand(
            target=obj,
            property_name="value",
            new_value=10,
            old_value=0,
            description="Change value to 10"
        )
        
        result = service.execute_command(command)
        assert obj.value == 10, "Command execution failed"
        logger.info("✓ Command executed successfully")
        
        # Test undo
        service.undo()
        assert obj.value == 0, "Undo failed"
        logger.info("✓ Undo successful")
        
        # Test redo
        service.redo()
        assert obj.value == 10, "Redo failed"
        logger.info("✓ Redo successful")
        
        # Test history
        history = service.get_history_summary()
        assert len(history) > 0, "No history found"
        logger.info(f"✓ History tracking: {len(history)} operations")
        
        logger.info("✅ Undo/Redo Service: PASSED\n")
        return True
        
    except Exception as e:
        logger.error(f"❌ Undo/Redo Service: FAILED - {e}\n")
        return False


def test_cache_service():
    """Test performance cache service."""
    logger.info("Testing Performance Cache Service...")
    
    try:
        from services.cache_service import PerformanceCacheService
        
        # Initialize service
        service = PerformanceCacheService(use_redis=False)
        
        # Test put/get
        service.put("test_key", {"data": "test_value"}, ttl=60)
        value, found = service.get("test_key")
        assert found, "Cache miss"
        assert value["data"] == "test_value", "Cache value mismatch"
        logger.info("✓ Cache put/get working")
        
        # Test invalidation
        service.invalidate("test_key")
        value, found = service.get("test_key")
        assert not found, "Cache invalidation failed"
        logger.info("✓ Cache invalidation working")
        
        # Test stats
        stats = service.get_stats()
        assert "hits" in stats, "Stats missing"
        logger.info(f"✓ Cache stats: {stats}")
        
        logger.info("✅ Performance Cache Service: PASSED\n")
        return True
        
    except Exception as e:
        logger.error(f"❌ Performance Cache Service: FAILED - {e}\n")
        return False


def test_ml_service():
    """Test ML prediction service."""
    logger.info("Testing ML Prediction Service...")
    
    try:
        from services.ml_prediction_service import MLPredictionService, PredictionTarget
        
        # Initialize service
        service = MLPredictionService()
        
        # Test assumptions
        test_assumptions = {
            "capacity_mw": 10,
            "capex_meur": 8.5,
            "production_mwh_year1": 18000,
            "ppa_price_eur_kwh": 0.045,
            "opex_eur_kw_year": 12,
            "debt_ratio": 0.75,
            "debt_interest_rate": 0.035,
            "debt_tenor_years": 15
        }
        
        # Test single prediction
        result = service.predict(test_assumptions, PredictionTarget.IRR_EQUITY)
        assert result.predicted_value > 0, "Invalid prediction"
        assert len(result.confidence_interval) == 2, "Invalid confidence interval"
        logger.info(f"✓ IRR prediction: {result.predicted_value:.2%}")
        
        # Test multiple predictions
        results = service.predict_multiple(test_assumptions)
        assert len(results) > 0, "No predictions returned"
        logger.info(f"✓ Multiple predictions: {len(results)} targets")
        
        # Test risk assessment
        risk = service.risk_assessment(test_assumptions)
        assert "risk_score" in risk, "Risk score missing"
        logger.info(f"✓ Risk assessment: {risk['overall_risk']}")
        
        logger.info("✅ ML Prediction Service: PASSED\n")
        return True
        
    except Exception as e:
        logger.error(f"❌ ML Prediction Service: FAILED - {e}\n")
        return False


def test_integration_service():
    """Test enhanced integration service."""
    logger.info("Testing Enhanced Integration Service...")
    
    try:
        from services.enhanced_integration_service import EnhancedIntegrationService
        
        # Initialize service
        service = EnhancedIntegrationService()
        
        # Test project data
        test_project = {
            "assumptions": {
                "capacity_mw": 10,
                "capex_meur": 8.5,
                "production_mwh_year1": 18000,
                "ppa_price_eur_kwh": 0.045
            }
        }
        
        # Test enhanced financial model
        results = service.run_enhanced_financial_model(
            test_project,
            include_ml_predictions=True,
            include_monte_carlo=True
        )
        
        assert "kpis" in results, "KPIs missing"
        assert "ml_predictions" in results, "ML predictions missing"
        assert "monte_carlo" in results, "Monte Carlo missing"
        logger.info("✓ Enhanced financial model executed")
        
        # Test system status
        status = service.get_system_status()
        assert "integration_service" in status, "Status missing"
        logger.info(f"✓ System status: {status['integration_service']['features_enabled']}")
        
        logger.info("✅ Enhanced Integration Service: PASSED\n")
        return True
        
    except Exception as e:
        logger.error(f"❌ Enhanced Integration Service: FAILED - {e}\n")
        return False


def main():
    """Run all tests."""
    logger.info("="*60)
    logger.info("ENHANCED FEATURES TEST SUITE")
    logger.info("="*60 + "\n")
    
    tests = [
        ("Data Persistence", test_persistence_service),
        ("Undo/Redo", test_undo_redo_service),
        ("Performance Cache", test_cache_service),
        ("ML Predictions", test_ml_service),
        ("Integration Service", test_integration_service)
    ]
    
    results = []
    for name, test_func in tests:
        try:
            passed = test_func()
            results.append((name, passed))
        except Exception as e:
            logger.error(f"Test {name} crashed: {e}")
            results.append((name, False))
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("TEST SUMMARY")
    logger.info("="*60)
    
    passed_count = sum(1 for _, p in results if p)
    total = len(results)
    
    for name, test_passed in results:
        status = "✅ PASSED" if test_passed else "❌ FAILED"
        logger.info(f"{name:.<40} {status}")
    
    logger.info(f"\nTotal: {passed_count}/{total} tests passed")
    
    if passed_count == total:
        logger.info("\n🎉 ALL TESTS PASSED! Enhanced features are working correctly.")
    else:
        logger.error(f"\n⚠️  {total - passed_count} tests failed. Please check the errors above.")
    
    return passed_count == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 