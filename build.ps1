# Hiel Renewable Energy Financial Modeler - PowerShell Build Script
# ================================================================

Write-Host ""
Write-Host "🏗️  Hiel RnE Financial Modeler Build Tool" -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Cyan
Write-Host ""

# Check if Python is available
try {
    $pythonVersion = python --version 2>$null
    Write-Host "✓ Python found: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python not found! Please install Python first." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Show build options
Write-Host "Available build options:" -ForegroundColor Yellow
Write-Host "1. Standard build (recommended)"
Write-Host "2. Debug build"
Write-Host "3. Clean + Standard build"
Write-Host "4. PyInstaller build (fallback)"
Write-Host "5. Check dependencies only"
Write-Host "6. Exit"
Write-Host ""

$choice = Read-Host "Choose option (1-6)"

switch ($choice) {
    "1" {
        Write-Host ""
        Write-Host "🔨 Starting standard build..." -ForegroundColor Yellow
        python build_exe.py
    }
    "2" {
        Write-Host ""
        Write-Host "🔨 Starting debug build..." -ForegroundColor Yellow
        python build_exe.py --debug
    }
    "3" {
        Write-Host ""
        Write-Host "🔨 Starting clean build..." -ForegroundColor Yellow
        python build_exe.py --clean
    }
    "4" {
        Write-Host ""
        Write-Host "🔨 Starting PyInstaller build..." -ForegroundColor Yellow
        python build_exe.py --pyinstaller
    }
    "5" {
        Write-Host ""
        Write-Host "🔍 Checking dependencies..." -ForegroundColor Yellow
        
        $packages = @("flet", "numpy", "pandas", "numpy_financial")
        $allInstalled = $true
        
        foreach ($package in $packages) {
            try {
                python -c "import $package; print(f'✓ $package: {$package.__version__ if hasattr($package, '__version__') else 'installed'}')" 2>$null
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "✓ $package: installed" -ForegroundColor Green
                } else {
                    Write-Host "❌ $package: not installed" -ForegroundColor Red
                    $allInstalled = $false
                }
            } catch {
                Write-Host "❌ $package: not installed" -ForegroundColor Red
                $allInstalled = $false
            }
        }
        
        if ($allInstalled) {
            Write-Host ""
            Write-Host "✅ All dependencies are installed!" -ForegroundColor Green
        } else {
            Write-Host ""
            Write-Host "❌ Some dependencies are missing. Run:" -ForegroundColor Red
            Write-Host "pip install flet numpy pandas numpy_financial" -ForegroundColor Yellow
        }
        
        Write-Host ""
        Read-Host "Press Enter to continue"
        exit 0
    }
    "6" {
        Write-Host "Goodbye!" -ForegroundColor Green
        exit 0
    }
    default {
        Write-Host "Invalid choice. Please run again." -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

Write-Host ""

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build failed!" -ForegroundColor Red
    Write-Host "Check the error messages above." -ForegroundColor Yellow
} else {
    Write-Host "✅ Build completed!" -ForegroundColor Green
    Write-Host "Check the 'dist' folder for your executable." -ForegroundColor Yellow
    Write-Host ""
    
    # Check if executable exists and show info
    $exePath = "dist\HielRnEModeler.exe"
    if (Test-Path $exePath) {
        $fileSize = (Get-Item $exePath).Length / 1MB
        Write-Host "📦 Executable: $exePath" -ForegroundColor Cyan
        Write-Host "📏 File size: $([math]::Round($fileSize, 1)) MB" -ForegroundColor Cyan
        Write-Host ""
        
        $openFolder = Read-Host "Open dist folder? (y/n)"
        if ($openFolder -eq "y" -or $openFolder -eq "Y") {
            Invoke-Item "dist"
        }
    }
}

Write-Host ""
Read-Host "Press Enter to exit" 