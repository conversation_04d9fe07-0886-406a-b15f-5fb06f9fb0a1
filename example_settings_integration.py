#!/usr/bin/env python3
"""
Example Settings Integration
===========================

Example showing how to integrate project settings view with JSON configuration.
"""

import flet as ft
import sys
from pathlib import Path

# Add the project root to Python path
sys.path.append(str(Path(__file__).parent))

from config import AppConfig, UIConfig, ExportConfig
from views.project_settings_view import ProjectSettingsView
from services.config_service import ConfigurationService


class SettingsApp:
    """Example application with settings functionality."""
    
    def __init__(self, page: ft.Page):
        self.page = page
        self.page.title = "Enhanced Financial Model - Settings"
        self.page.theme_mode = ft.ThemeMode.LIGHT
        self.page.padding = 0
        
        # Initialize configurations
        self.app_config = AppConfig()
        self.ui_config = UIConfig()
        self.export_config = ExportConfig()
        self.config_service = ConfigurationService()
        
        # Load saved configuration if exists
        self._load_saved_configuration()
        
        # Create settings view
        self.settings_view = ProjectSettingsView(
            app_config=self.app_config,
            ui_config=self.ui_config,
            export_config=self.export_config
        )
        
        # Set page for settings view
        self.settings_view.page = page
        
        # Build UI
        self._build_ui()
    
    def _load_saved_configuration(self):
        """Load previously saved configuration."""
        try:
            config_file = Path("config/saved_config.json")
            if config_file.exists():
                config_data = self.config_service.load_configuration_from_json(config_file)
                self.config_service.apply_configuration(
                    config_data, self.app_config, self.ui_config, self.export_config
                )
                print(f"✅ Loaded saved configuration")
        except Exception as e:
            print(f"⚠️ Could not load saved configuration: {str(e)}")
    
    def _build_ui(self):
        """Build the main UI."""
        # Create app bar
        app_bar = ft.AppBar(
            title=ft.Text(f"{self.app_config.app_name} - Settings"),
            bgcolor=ft.Colors.BLUE,
            color=ft.Colors.WHITE,
            actions=[
                ft.IconButton(
                    icon=ft.Icons.SAVE,
                    tooltip="Save Configuration",
                    on_click=self._quick_save_config
                ),
                ft.IconButton(
                    icon=ft.Icons.FOLDER_OPEN,
                    tooltip="Load Configuration",
                    on_click=self._quick_load_config
                )
            ]
        )
        
        # Add to page
        self.page.appbar = app_bar
        
        # Add settings view content
        settings_content = self.settings_view.build_content()
        self.page.add(settings_content)
        
        # Update page
        self.page.update()
    
    def _quick_save_config(self, e):
        """Quick save configuration."""
        try:
            config_file = Path("config/saved_config.json")
            success = self.config_service.save_configuration_to_json(
                self.app_config, self.ui_config, self.export_config, config_file
            )
            if success:
                self._show_message("Configuration saved successfully!", ft.Colors.GREEN)
            else:
                self._show_message("Could not save configuration", ft.Colors.RED)
        except Exception as ex:
            self._show_message(f"Save error: {str(ex)}", ft.Colors.RED)
    
    def _quick_load_config(self, e):
        """Quick load configuration."""
        try:
            config_file = Path("config/saved_config.json")
            if config_file.exists():
                config_data = self.config_service.load_configuration_from_json(config_file)
                success = self.config_service.apply_configuration(
                    config_data, self.app_config, self.ui_config, self.export_config
                )
                if success:
                    self.settings_view.refresh()
                    self._show_message("Configuration loaded successfully!", ft.Colors.GREEN)
                else:
                    self._show_message("Could not apply configuration", ft.Colors.RED)
            else:
                self._show_message("No saved configuration found", ft.Colors.ORANGE)
        except Exception as ex:
            self._show_message(f"Load error: {str(ex)}", ft.Colors.RED)
    
    def _show_message(self, message: str, color: str):
        """Show a message snackbar."""
        snackbar = ft.SnackBar(
            content=ft.Text(message),
            bgcolor=color,
            duration=3000
        )
        self.page.snack_bar = snackbar
        snackbar.open = True
        self.page.update()


def main(page: ft.Page):
    """Main application entry point."""
    app = SettingsApp(page)


if __name__ == "__main__":
    print("🚀 Starting Enhanced Financial Model Settings")
    print("=" * 50)
    print("Features:")
    print("✅ JSON Configuration Loading/Saving")
    print("✅ Project Settings Management")
    print("✅ Template Management")
    print("✅ Export Settings")
    print("=" * 50)
    
    # Run the Flet app
    ft.app(target=main, view=ft.AppView.WEB_BROWSER, port=8080) 