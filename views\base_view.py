"""
Base View Component
===================

Base class for all view components with common functionality.
"""

import flet as ft
import logging
from typing import Dict, Any, Optional, Callable
from abc import ABC, abstractmethod

from models.ui_state import UIState
from models.client_profile import ClientProfile
from models.project_assumptions import EnhancedProjectAssumptions


class BaseView(ABC):
    """Base class for all view components."""
    
    def __init__(self, page: ft.Page):
        self.page = page
        self._content: Optional[ft.Control] = None
        self._is_built = False

        # Initialize logger
        self.logger = logging.getLogger(self.__class__.__name__)

        # Callbacks for communication with controller
        self.on_navigate: Optional[Callable[[str], None]] = None
        self.on_data_changed: Optional[Callable[[str, Any], None]] = None
        self.on_action_requested: Optional[Callable[[str, Dict[str, Any]], None]] = None
        self.on_status_update: Optional[Callable[[str, str], None]] = None
    
    @abstractmethod
    def build_content(self) -> ft.Control:
        """Build the view content. Must be implemented by subclasses."""
        pass
    
    def get_content(self) -> ft.Control:
        """Get the view content, building it if necessary."""
        if not self._is_built or self._content is None:
            self._content = self.build_content()
            self._is_built = True
        return self._content
    
    def refresh(self):
        """Refresh the view by rebuilding content."""
        self._is_built = False
        self._content = None
    
    def update_data(self, data: Dict[str, Any]):
        """Update view with new data. Override in subclasses as needed."""
        pass
    
    def set_loading(self, loading: bool, message: str = ""):
        """Set loading state for the view."""
        if self.on_status_update:
            status = "loading" if loading else "ready"
            self.on_status_update(status, message)
    
    def show_error(self, error: str):
        """Show error message."""
        if self.on_status_update:
            self.on_status_update("error", error)
    
    def show_success(self, message: str):
        """Show success message."""
        if self.on_status_update:
            self.on_status_update("success", message)

    def show_notification(self, message: str, color: str = None):
        """Show notification message."""
        # For now, use the existing status update mechanism
        # In a full implementation, this could show a snackbar or toast
        self.logger.info(f"Notification: {message}")
        if self.on_status_update:
            self.on_status_update("notification", message)
    
    def navigate_to(self, tab: str):
        """Navigate to another tab."""
        if self.on_navigate:
            self.on_navigate(tab)
    
    def request_action(self, action: str, params: Dict[str, Any] = None):
        """Request an action from the controller."""
        if self.on_action_requested:
            self.on_action_requested(action, params or {})
    
    def notify_data_changed(self, data_type: str, data: Any):
        """Notify controller of data changes."""
        if self.on_data_changed:
            self.on_data_changed(data_type, data)
    
    # Common UI helper methods
    def create_card(self, title: str, content: ft.Control, 
                   icon: Optional[str] = None, 
                   bgcolor: Optional[str] = None) -> ft.Card:
        """Create a standard card with title and content."""
        header_controls = []
        
        if icon:
            header_controls.append(ft.Icon(icon, color=ft.Colors.BLUE_600))
        
        header_controls.append(ft.Text(title, size=18, weight=ft.FontWeight.BOLD))
        
        card_content = ft.Column([
            ft.Row(header_controls),
            ft.Divider(height=10),
            content
        ])
        
        return ft.Card(
            content=ft.Container(
                content=card_content,
                padding=20,
                bgcolor=bgcolor
            )
        )
    
    def create_kpi_card(self, title: str, value: str, 
                       color: str = ft.Colors.BLUE,
                       icon: Optional[str] = None) -> ft.Card:
        """Create a KPI display card."""
        content_controls = []
        
        if icon:
            content_controls.append(ft.Icon(icon, color=color, size=30))
        
        content_controls.extend([
            ft.Text(title, size=14, color=ft.Colors.GREY_700),
            ft.Text(value, size=20, weight=ft.FontWeight.BOLD, color=color)
        ])
        
        return ft.Card(
            content=ft.Container(
                content=ft.Column(
                    content_controls,
                    alignment=ft.MainAxisAlignment.CENTER,
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER
                ),
                padding=20,
                width=200,
                height=120
            )
        )
    
    def create_section_header(self, title: str, subtitle: str = "") -> ft.Container:
        """Create a section header."""
        controls = [ft.Text(title, size=24, weight=ft.FontWeight.BOLD)]
        
        if subtitle:
            controls.append(ft.Text(subtitle, size=14, color=ft.Colors.GREY_600))
        
        return ft.Container(
            content=ft.Column(controls),
            padding=ft.padding.only(bottom=20)
        )
    
    def create_loading_indicator(self, message: str = "Loading...") -> ft.Container:
        """Create a loading indicator."""
        return ft.Container(
            content=ft.Column([
                ft.ProgressRing(),
                ft.Text(message, size=16, text_align=ft.TextAlign.CENTER)
            ], 
            alignment=ft.MainAxisAlignment.CENTER,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            alignment=ft.alignment.center,
            expand=True
        )
    
    def create_error_display(self, error: str) -> ft.Container:
        """Create an error display."""
        return ft.Container(
            content=ft.Column([
                ft.Icon(ft.Icons.ERROR, color=ft.Colors.RED, size=48),
                ft.Text("Error", size=20, weight=ft.FontWeight.BOLD, color=ft.Colors.RED),
                ft.Text(error, size=14, text_align=ft.TextAlign.CENTER, color=ft.Colors.RED_700)
            ],
            alignment=ft.MainAxisAlignment.CENTER,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER),
            alignment=ft.alignment.center,
            expand=True
        )
    
    def create_empty_state(self, title: str, message: str, 
                          action_text: str = "", 
                          action_callback: Optional[Callable] = None) -> ft.Container:
        """Create an empty state display."""
        controls = [
            ft.Icon(ft.Icons.INFO_OUTLINE, color=ft.Colors.GREY, size=48),
            ft.Text(title, size=20, weight=ft.FontWeight.BOLD, color=ft.Colors.GREY_700),
            ft.Text(message, size=14, text_align=ft.TextAlign.CENTER, color=ft.Colors.GREY_600)
        ]
        
        if action_text and action_callback:
            controls.append(
                ft.ElevatedButton(
                    action_text,
                    on_click=lambda _: action_callback(),
                    bgcolor=ft.Colors.BLUE_600,
                    color=ft.Colors.WHITE
                )
            )
        
        return ft.Container(
            content=ft.Column(
                controls,
                alignment=ft.MainAxisAlignment.CENTER,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                spacing=20
            ),
            alignment=ft.alignment.center,
            expand=True
        )
    
    def create_action_button(self, text: str, icon: str, 
                           on_click: Callable, 
                           color: str = ft.Colors.BLUE_600) -> ft.ElevatedButton:
        """Create a standard action button."""
        return ft.ElevatedButton(
            text,
            icon=icon,
            on_click=on_click,
            bgcolor=color,
            color=ft.Colors.WHITE,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=8)
            )
        )
    
    def create_info_row(self, label: str, value: str, 
                       value_color: str = ft.Colors.BLACK) -> ft.Row:
        """Create an information row."""
        return ft.Row([
            ft.Text(f"{label}:", size=14, weight=ft.FontWeight.BOLD, expand=1),
            ft.Text(str(value), size=14, color=value_color, expand=2)
        ])
    
    def create_progress_bar(self, value: float, max_value: float = 100.0, 
                           color: str = ft.Colors.BLUE) -> ft.ProgressBar:
        """Create a progress bar."""
        return ft.ProgressBar(
            value=value / max_value,
            color=color,
            bgcolor=ft.Colors.GREY_300,
            height=8
        )
    
    def format_currency(self, value: float, currency: str = "EUR") -> str:
        """Format currency value."""
        if abs(value) >= 1e6:
            return f"{value/1e6:.2f}M {currency}"
        elif abs(value) >= 1e3:
            return f"{value/1e3:.1f}k {currency}"
        else:
            return f"{value:.2f} {currency}"
    
    def format_percentage(self, value: float) -> str:
        """Format percentage value."""
        return f"{value*100:.1f}%"
    
    def format_number(self, value: float, decimals: int = 2) -> str:
        """Format number with thousands separator."""
        return f"{value:,.{decimals}f}"
