"""
Test Services
=============

Unit tests for service layer components.
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import pandas as pd
from datetime import datetime

from services.financial_service import FinancialModelService
from services.validation_service import ValidationService
from services.export_service import ExportService
from models.project_assumptions import EnhancedProjectAssumptions
from models.client_profile import ClientProfile


class TestFinancialModelService(unittest.TestCase):
    """Test cases for FinancialModelService."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.service = FinancialModelService()
        self.assumptions = EnhancedProjectAssumptions()
        # Set valid test assumptions
        self.assumptions.capacity_mw = 10.0
        self.assumptions.production_mwh_year1 = 18000.0
        self.assumptions.capex_meur = 8.5
        self.assumptions.opex_keuros_year1 = 180.0
        self.assumptions.ppa_price_eur_kwh = 0.045
    
    @patch('src.new_app.services.financial_service.run_enhanced_financial_model')
    def test_run_financial_model_success(self, mock_run_model):
        """Test successful financial model execution."""
        # Mock the core financial model function
        mock_results = {
            'kpis': {
                'IRR_project': 0.12,
                'IRR_equity': 0.15,
                'NPV_project': 5000000,
                'LCOE_eur_kwh': 0.042
            },
            'cashflow': pd.DataFrame({
                'Year': [1, 2, 3],
                'Revenue': [810000, 810000, 810000],
                'OPEX': [-180000, -180000, -180000],
                'Equity_CF': [100000, 200000, 300000]
            })
        }
        mock_run_model.return_value = mock_results
        
        # Run the service
        results = self.service.run_financial_model(self.assumptions)
        
        # Assertions
        self.assertIsNotNone(results)
        self.assertIn('kpis', results)
        self.assertIn('cashflow', results)
        self.assertEqual(results['kpis']['IRR_project'], 0.12)
        mock_run_model.assert_called_once()
    
    def test_run_financial_model_invalid_assumptions(self):
        """Test financial model with invalid assumptions."""
        # Set invalid assumptions
        invalid_assumptions = EnhancedProjectAssumptions()
        invalid_assumptions.capacity_mw = -5.0  # Invalid negative capacity
        
        # Should raise an exception or return error
        with self.assertRaises(Exception):
            self.service.run_financial_model(invalid_assumptions)
    
    def test_run_sensitivity_analysis(self):
        """Test sensitivity analysis execution."""
        variables = ['production_mwh_year1', 'ppa_price_eur_kwh']
        
        # Mock the sensitivity analysis
        with patch.object(self.service, '_run_sensitivity_analysis') as mock_sensitivity:
            mock_sensitivity.return_value = pd.DataFrame({
                'Variable': ['production_mwh_year1', 'ppa_price_eur_kwh'],
                'Base_Case': [18000, 0.045],
                'Low_Case': [16200, 0.040],
                'High_Case': [19800, 0.050],
                'IRR_Low': [0.10, 0.08],
                'IRR_High': [0.14, 0.16]
            })
            
            results = self.service.run_sensitivity_analysis(self.assumptions, variables)
            
            self.assertIsInstance(results, pd.DataFrame)
            self.assertEqual(len(results), 2)
            mock_sensitivity.assert_called_once()
    
    def test_run_monte_carlo_simulation(self):
        """Test Monte Carlo simulation execution."""
        n_simulations = 100
        
        # Mock the Monte Carlo simulation
        with patch.object(self.service, '_run_monte_carlo_simulation') as mock_mc:
            mock_mc.return_value = {
                'statistics': {
                    'mean_irr': 0.12,
                    'std_irr': 0.02,
                    'percentile_5': 0.08,
                    'percentile_95': 0.16
                },
                'n_simulations': n_simulations,
                'results': [0.10, 0.11, 0.12, 0.13, 0.14] * 20  # Mock results
            }
            
            results = self.service.run_monte_carlo_simulation(self.assumptions, n_simulations)
            
            self.assertIsNotNone(results)
            self.assertIn('statistics', results)
            self.assertEqual(results['n_simulations'], n_simulations)
            mock_mc.assert_called_once()


class TestValidationService(unittest.TestCase):
    """Test cases for ValidationService."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.service = ValidationService()
        self.assumptions = EnhancedProjectAssumptions()
        self.kpis = {
            'IRR_project': 0.12,
            'IRR_equity': 0.15,
            'NPV_project': 5000000,
            'LCOE_eur_kwh': 0.042,
            'Min_DSCR': 1.35
        }
        self.cashflow = pd.DataFrame({
            'Year': [1, 2, 3],
            'Revenue': [810000, 810000, 810000],
            'OPEX': [-180000, -180000, -180000],
            'DSCR': [1.4, 1.3, 1.5]
        })
    
    def test_validate_model_success(self):
        """Test successful model validation."""
        results = self.service.validate_model(self.assumptions, self.kpis, self.cashflow)
        
        self.assertIsNotNone(results)
        self.assertTrue(results.is_valid)
        self.assertIsInstance(results.warnings, list)
        self.assertIsInstance(results.errors, list)
    
    def test_validate_model_with_errors(self):
        """Test model validation with errors."""
        # Create invalid KPIs
        invalid_kpis = self.kpis.copy()
        invalid_kpis['IRR_project'] = -0.05  # Negative IRR
        invalid_kpis['Min_DSCR'] = 0.8  # Low DSCR
        
        results = self.service.validate_model(self.assumptions, invalid_kpis, self.cashflow)
        
        self.assertFalse(results.is_valid)
        self.assertGreater(len(results.errors), 0)
    
    def test_generate_benchmark_comparison(self):
        """Test benchmark comparison generation."""
        results = self.service.generate_benchmark_comparison(self.assumptions, self.kpis)
        
        self.assertIsNotNone(results)
        self.assertIn('custom_analysis', results)
        
        custom_analysis = results['custom_analysis']
        self.assertIn('irr_project', custom_analysis)
        self.assertIn('lcoe', custom_analysis)
    
    def test_validate_assumptions_only(self):
        """Test validation of assumptions only."""
        # Test with valid assumptions
        results = self.service._validate_assumptions(self.assumptions)
        self.assertTrue(results['is_valid'])
        
        # Test with invalid assumptions
        invalid_assumptions = EnhancedProjectAssumptions()
        invalid_assumptions.capacity_mw = -5.0
        invalid_assumptions.debt_ratio = 1.5
        
        results = self.service._validate_assumptions(invalid_assumptions)
        self.assertFalse(results['is_valid'])
        self.assertGreater(len(results['errors']), 0)


class TestExportService(unittest.TestCase):
    """Test cases for ExportService."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.service = ExportService()
        self.client_profile = ClientProfile()
        self.client_profile.company_name = "Test Company"
        self.client_profile.client_name = "John Doe"
        self.client_profile.project_name = "Test Project"
        
        self.assumptions = EnhancedProjectAssumptions()
        self.financial_results = {
            'kpis': {
                'IRR_project': 0.12,
                'IRR_equity': 0.15,
                'NPV_project': 5000000
            },
            'cashflow': pd.DataFrame({
                'Year': [1, 2, 3],
                'Revenue': [810000, 810000, 810000],
                'OPEX': [-180000, -180000, -180000]
            })
        }
    
    @patch('src.new_app.services.export_service.export_enhanced_excel')
    @patch('src.new_app.utils.file_utils.FileUtils.create_timestamped_output_directory')
    def test_export_excel_report(self, mock_create_dir, mock_export_excel):
        """Test Excel report export."""
        # Mock directory creation
        mock_create_dir.return_value = {
            'data_dir': '/mock/path/data',
            'reports_dir': '/mock/path/reports'
        }
        
        # Mock Excel export
        mock_export_excel.return_value = None
        
        # Run export
        result = self.service.export_excel_report(
            self.client_profile,
            self.assumptions,
            self.financial_results
        )
        
        # Assertions
        self.assertIsNotNone(result)
        mock_create_dir.assert_called_once()
        mock_export_excel.assert_called_once()
    
    @patch('docx.Document')
    @patch('src.new_app.utils.file_utils.FileUtils.create_timestamped_output_directory')
    def test_export_docx_report(self, mock_create_dir, mock_document):
        """Test DOCX report export."""
        # Mock directory creation
        mock_create_dir.return_value = {
            'data_dir': '/mock/path/data',
            'reports_dir': '/mock/path/reports'
        }
        
        # Mock Document
        mock_doc = MagicMock()
        mock_document.return_value = mock_doc
        
        # Run export
        result = self.service.export_docx_report(
            self.client_profile,
            self.assumptions,
            self.financial_results
        )
        
        # Assertions
        self.assertIsNotNone(result)
        mock_create_dir.assert_called_once()
        mock_doc.save.assert_called_once()
    
    def test_export_json_data(self):
        """Test JSON data export."""
        with patch('builtins.open', create=True) as mock_open:
            with patch('json.dump') as mock_json_dump:
                with patch('src.new_app.utils.file_utils.FileUtils.create_timestamped_output_directory') as mock_create_dir:
                    mock_create_dir.return_value = {
                        'data_dir': '/mock/path/data'
                    }
                    
                    result = self.service.export_json_data(
                        self.client_profile,
                        self.assumptions,
                        self.financial_results
                    )
                    
                    self.assertIsNotNone(result)
                    mock_open.assert_called_once()
                    mock_json_dump.assert_called_once()
    
    def test_serialize_financial_results(self):
        """Test financial results serialization."""
        serialized = self.service._serialize_financial_results(self.financial_results)
        
        self.assertIsNotNone(serialized)
        self.assertIn('kpis', serialized)
        self.assertIn('cashflow', serialized)
        
        # Check that DataFrame was converted to records
        self.assertIsInstance(serialized['cashflow'], list)


if __name__ == '__main__':
    unittest.main()
