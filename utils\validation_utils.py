"""
Validation Utilities
====================

Utility functions for data validation.
"""

import re
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import logging


class ValidationUtils:
    """Utility class for data validation."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    @staticmethod
    def validate_email(email: str) -> bool:
        """Validate email format."""
        if not email:
            return True  # Empty email is optional
        
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email.strip()) is not None
    
    @staticmethod
    def validate_phone(phone: str) -> bool:
        """Validate phone number format."""
        if not phone:
            return True  # Empty phone is optional
        
        # Remove common separators
        clean_phone = re.sub(r'[\s\-\(\)\+]', '', phone)
        # Check if it contains only digits and is reasonable length
        return clean_phone.isdigit() and 7 <= len(clean_phone) <= 15
    
    @staticmethod
    def validate_positive_number(value: Union[int, float], allow_zero: bool = False) -> bool:
        """Validate that a number is positive."""
        if value is None:
            return False
        
        try:
            num_value = float(value)
            return num_value > 0 or (allow_zero and num_value >= 0)
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def validate_percentage(value: Union[int, float], min_val: float = 0.0, max_val: float = 1.0) -> bool:
        """Validate that a value is within percentage range."""
        if value is None:
            return False
        
        try:
            num_value = float(value)
            return min_val <= num_value <= max_val
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def validate_range(value: Union[int, float], min_val: float, max_val: float) -> bool:
        """Validate that a value is within specified range."""
        if value is None:
            return False
        
        try:
            num_value = float(value)
            return min_val <= num_value <= max_val
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def validate_required_string(value: str) -> bool:
        """Validate that a string is not empty."""
        return bool(value and value.strip())
    
    @staticmethod
    def validate_capacity_factor(production_mwh: float, capacity_mw: float) -> Dict[str, Any]:
        """Validate capacity factor calculation."""
        result = {
            'is_valid': True,
            'capacity_factor': 0.0,
            'warnings': [],
            'errors': []
        }
        
        if not ValidationUtils.validate_positive_number(production_mwh):
            result['is_valid'] = False
            result['errors'].append("Production must be a positive number")
            return result
        
        if not ValidationUtils.validate_positive_number(capacity_mw):
            result['is_valid'] = False
            result['errors'].append("Capacity must be a positive number")
            return result
        
        # Calculate capacity factor
        max_annual_production = capacity_mw * 8760  # MWh
        capacity_factor = production_mwh / max_annual_production
        result['capacity_factor'] = capacity_factor
        
        # Validate capacity factor range
        if capacity_factor > 0.6:
            result['warnings'].append(f"High capacity factor ({capacity_factor:.1%}) - verify production estimate")
        elif capacity_factor < 0.1:
            result['warnings'].append(f"Low capacity factor ({capacity_factor:.1%}) - may impact economics")
        
        return result
    
    @staticmethod
    def validate_financial_ratios(debt_ratio: float, interest_rate: float, 
                                discount_rate: float) -> Dict[str, Any]:
        """Validate financial ratios."""
        result = {
            'is_valid': True,
            'warnings': [],
            'errors': []
        }
        
        # Validate debt ratio
        if not ValidationUtils.validate_percentage(debt_ratio, 0.0, 0.95):
            result['is_valid'] = False
            result['errors'].append("Debt ratio must be between 0% and 95%")
        elif debt_ratio > 0.85:
            result['warnings'].append("High debt ratio may increase financial risk")
        
        # Validate interest rate
        if not ValidationUtils.validate_percentage(interest_rate, 0.0, 0.25):
            result['is_valid'] = False
            result['errors'].append("Interest rate must be between 0% and 25%")
        elif interest_rate > 0.15:
            result['warnings'].append("High interest rate may impact project viability")
        
        # Validate discount rate
        if not ValidationUtils.validate_percentage(discount_rate, 0.0, 0.30):
            result['is_valid'] = False
            result['errors'].append("Discount rate must be between 0% and 30%")
        
        # Cross-validation
        if result['is_valid'] and discount_rate < interest_rate:
            result['warnings'].append("Discount rate is lower than interest rate - verify assumptions")
        
        return result
    
    @staticmethod
    def validate_project_timeline(project_life: int, debt_years: int) -> Dict[str, Any]:
        """Validate project timeline parameters."""
        result = {
            'is_valid': True,
            'warnings': [],
            'errors': []
        }
        
        # Validate project life
        if not ValidationUtils.validate_range(project_life, 10, 50):
            result['is_valid'] = False
            result['errors'].append("Project life must be between 10 and 50 years")
        
        # Validate debt years
        if not ValidationUtils.validate_range(debt_years, 5, 25):
            result['is_valid'] = False
            result['errors'].append("Debt tenor must be between 5 and 25 years")
        
        # Cross-validation
        if result['is_valid'] and debt_years > project_life:
            result['is_valid'] = False
            result['errors'].append("Debt tenor cannot exceed project life")
        elif result['is_valid'] and debt_years > project_life * 0.8:
            result['warnings'].append("Long debt tenor relative to project life")
        
        return result
    
    @staticmethod
    def validate_grants(grants: Dict[str, float], capex: float) -> Dict[str, Any]:
        """Validate grant amounts."""
        result = {
            'is_valid': True,
            'warnings': [],
            'errors': []
        }
        
        total_grants = sum(grants.values())
        
        # Validate individual grants
        for grant_name, amount in grants.items():
            if amount < 0:
                result['is_valid'] = False
                result['errors'].append(f"{grant_name} cannot be negative")
        
        # Validate total grants
        if total_grants > capex:
            result['is_valid'] = False
            result['errors'].append("Total grants cannot exceed CAPEX")
        elif total_grants > capex * 0.5:
            result['warnings'].append("High grant percentage - verify eligibility")
        
        return result
    
    @staticmethod
    def validate_ppa_price(ppa_price: float, market_reference: float = 0.045) -> Dict[str, Any]:
        """Validate PPA price against market reference."""
        result = {
            'is_valid': True,
            'warnings': [],
            'errors': []
        }
        
        if not ValidationUtils.validate_positive_number(ppa_price):
            result['is_valid'] = False
            result['errors'].append("PPA price must be positive")
            return result
        
        if ppa_price > 0.15:
            result['warnings'].append("Very high PPA price - verify market conditions")
        elif ppa_price < 0.02:
            result['warnings'].append("Very low PPA price - may impact viability")
        elif ppa_price > market_reference * 1.5:
            result['warnings'].append("PPA price significantly above market reference")
        elif ppa_price < market_reference * 0.7:
            result['warnings'].append("PPA price significantly below market reference")
        
        return result
    
    @staticmethod
    def validate_comprehensive(data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform comprehensive validation of all parameters."""
        result = {
            'is_valid': True,
            'warnings': [],
            'errors': [],
            'field_validations': {}
        }
        
        # Validate capacity factor
        if 'production_mwh_year1' in data and 'capacity_mw' in data:
            cf_validation = ValidationUtils.validate_capacity_factor(
                data['production_mwh_year1'], 
                data['capacity_mw']
            )
            result['field_validations']['capacity_factor'] = cf_validation
            if not cf_validation['is_valid']:
                result['is_valid'] = False
            result['warnings'].extend(cf_validation['warnings'])
            result['errors'].extend(cf_validation['errors'])
        
        # Validate financial ratios
        if all(key in data for key in ['debt_ratio', 'interest_rate', 'discount_rate']):
            fr_validation = ValidationUtils.validate_financial_ratios(
                data['debt_ratio'], 
                data['interest_rate'], 
                data['discount_rate']
            )
            result['field_validations']['financial_ratios'] = fr_validation
            if not fr_validation['is_valid']:
                result['is_valid'] = False
            result['warnings'].extend(fr_validation['warnings'])
            result['errors'].extend(fr_validation['errors'])
        
        # Validate project timeline
        if 'project_life_years' in data and 'debt_years' in data:
            pt_validation = ValidationUtils.validate_project_timeline(
                data['project_life_years'], 
                data['debt_years']
            )
            result['field_validations']['project_timeline'] = pt_validation
            if not pt_validation['is_valid']:
                result['is_valid'] = False
            result['warnings'].extend(pt_validation['warnings'])
            result['errors'].extend(pt_validation['errors'])
        
        return result
