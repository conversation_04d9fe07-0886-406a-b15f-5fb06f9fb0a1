"""
Test Export Functionality
=========================

Comprehensive tests to verify that all export formats include charts, DCF tables, 
and detailed analysis, and that files are properly saved to output directories.
"""

import unittest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import pandas as pd
import json

from models.client_profile import ClientProfile
from models.project_assumptions import EnhancedProjectAssumptions
from services.export_service import ExportService
from services.report_service import ReportGenerationService
from utils.file_utils import FileUtils
from components.charts.chart_factory import ChartFactory
from config.export_config import ExportConfig


class TestExportFunctionality(unittest.TestCase):
    """Test export functionality comprehensively."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.temp_path = Path(self.temp_dir)
        
        # Create test client profile
        self.client_profile = ClientProfile(
            company_name="Test Solar Company",
            project_name="Test Solar Project",
            project_location="Test Location",
            consultant="Test Consultant",
            consultant_website="www.test.com",
            tagline="Test Tagline"
        )
        
        # Create test assumptions
        self.assumptions = EnhancedProjectAssumptions(
            capacity_mw=100.0,
            capex_meur=80.0,
            opex_keuros_year1=2500.0,
            ppa_price_eur_kwh=0.045,
            degradation_rate=0.005,
            project_life_years=25
        )
        
        # Create test financial results
        self.financial_results = {
            'kpis': {
                'IRR_project': 0.125,
                'IRR_equity': 0.145,
                'NPV_project': 25000000,
                'NPV_equity': 15000000,
                'LCOE_eur_kwh': 0.042,
                'Min_DSCR': 1.35,
                'Payback_years': 8.5,
                'Terminal_value': 12000000,
                'Total_capex': 80000000,
                'Total_revenue': 150000000,
                'Total_opex': 45000000
            },
            'cashflow': pd.DataFrame({
                'Year': range(26),
                'Revenue': [0] + [6000000] * 25,
                'EBITDA': [0] + [4500000] * 25,
                'Free_Cash_Flow_Project': [-80000000] + [3500000] * 25,
                'Free_Cash_Flow_Equity': [-20000000] + [2800000] * 25,
                'CAPEX': [80000000] + [0] * 25,
                'Debt_Service': [0] + [1200000] * 15 + [0] * 10
            }),
            'dcf_assumptions': {
                'discount_rate': 0.08
            },
            'model_version': '3.0.0_DCF_Enhanced',
            'execution_time': '2.5 seconds'
        }
        
        # Initialize services
        self.export_service = ExportService()
        self.report_service = ReportGenerationService()
        self.file_utils = FileUtils()
        self.chart_factory = ChartFactory()
        self.export_config = ExportConfig()
    
    def tearDown(self):
        """Clean up test fixtures."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_output_directory_creation(self):
        """Test that output directories are created correctly."""
        # Create output directory structure
        directories = self.file_utils.create_timestamped_output_directory(self.client_profile)
        
        # Verify all required directories exist
        required_dirs = ['main_dir', 'data_dir', 'charts_dir', 'reports_dir', 'exports_dir', 'temp_dir']
        for dir_name in required_dirs:
            self.assertIn(dir_name, directories)
            self.assertTrue(directories[dir_name].exists())
            self.assertTrue(directories[dir_name].is_dir())
        
        # Verify subdirectories exist
        charts_subdirs = ['financial', 'analysis', 'comparison']
        for subdir in charts_subdirs:
            subdir_path = directories['charts_dir'] / subdir
            self.assertTrue(subdir_path.exists())
        
        data_subdirs = ['raw', 'processed', 'backup']
        for subdir in data_subdirs:
            subdir_path = directories['data_dir'] / subdir
            self.assertTrue(subdir_path.exists())
        
        reports_subdirs = ['excel', 'pdf', 'html']
        for subdir in reports_subdirs:
            subdir_path = directories['reports_dir'] / subdir
            self.assertTrue(subdir_path.exists())
    
    def test_chart_generation_and_export(self):
        """Test that charts are generated and saved to files."""
        output_dir = self.file_utils.create_timestamped_output_directory(self.client_profile)
        charts_dir = output_dir['charts_dir']
        
        # Test KPI bar chart
        kpi_data = {
            'Project IRR': 12.5,
            'Equity IRR': 14.5,
            'LCOE (c€/kWh)': 4.2,
            'Min DSCR': 1.35
        }
        
        chart_path = charts_dir / "test_financial_kpis.png"
        ui_component, chart_bytes = self.chart_factory.create_and_export_bar_chart(
            data=kpi_data,
            title="Test Financial KPIs",
            save_path=chart_path
        )
        
        # Verify chart file was created
        self.assertTrue(chart_path.exists())
        self.assertGreater(chart_path.stat().st_size, 0)
        
        # Verify chart bytes are returned
        self.assertIsInstance(chart_bytes, bytes)
        self.assertGreater(len(chart_bytes), 0)
        
        # Test cash flow line chart
        cashflow_data = self.financial_results['cashflow']
        chart_path = charts_dir / "test_cash_flow.png"
        ui_component, chart_bytes = self.chart_factory.create_and_export_line_chart(
            data=cashflow_data,
            title="Test Cash Flow Analysis",
            x_column='Year',
            y_columns=['Free_Cash_Flow_Project', 'Free_Cash_Flow_Equity'],
            save_path=chart_path
        )
        
        # Verify chart file was created
        self.assertTrue(chart_path.exists())
        self.assertGreater(chart_path.stat().st_size, 0)
        
        # Test DCF waterfall chart
        dcf_data = {
            'Initial Investment': -80000000,
            'Operating Cash Flows': 87500000,
            'Terminal Value': 12000000,
            'Net Present Value': 25000000
        }
        
        chart_path = charts_dir / "test_dcf_waterfall.png"
        ui_component, chart_bytes = self.chart_factory.create_dcf_waterfall_chart(
            cash_flows=dcf_data,
            title="Test DCF Analysis",
            save_path=chart_path
        )
        
        # Verify chart file was created
        self.assertTrue(chart_path.exists())
        self.assertGreater(chart_path.stat().st_size, 0)
    
    def test_excel_export_with_dcf_tables(self):
        """Test Excel export includes comprehensive DCF tables."""
        output_dir = self.file_utils.create_timestamped_output_directory(self.client_profile)
        
        # Create test sensitivity and Monte Carlo data
        sensitivity_df = pd.DataFrame({
            'Parameter': ['Electricity Price', 'CAPEX', 'Degradation Rate'],
            'Base_Value': [0.045, 80.0, 0.005],
            'Low_Case': [0.040, 85.0, 0.007],
            'High_Case': [0.050, 75.0, 0.003],
            'IRR_Impact': [0.025, -0.018, -0.012]
        })
        
        mc_stats = {
            'n_simulations': 1000,
            'IRR_project': {
                'mean': 0.125,
                'median': 0.123,
                'std': 0.025,
                'p5': 0.085,
                'p95': 0.165,
                'prob_above_12pct': 0.75
            },
            'NPV_project': {
                'mean': 25000000,
                'median': 24500000,
                'std': 8000000,
                'p5': 12000000,
                'p95': 38000000,
                'prob_positive': 0.92
            }
        }
        
        scenarios_dict = {
            'base_case': {'kpis': self.financial_results['kpis']},
            'optimistic': {'kpis': {**self.financial_results['kpis'], 'IRR_project': 0.155}},
            'pessimistic': {'kpis': {**self.financial_results['kpis'], 'IRR_project': 0.095}}
        }
        
        # Export Excel report
        excel_file = self.export_service.export_excel_report(
            client_profile=self.client_profile,
            assumptions=self.assumptions,
            financial_results=self.financial_results,
            sensitivity_results=sensitivity_df,
            monte_carlo_results=mc_stats,
            scenario_results=scenarios_dict,
            output_dir=output_dir
        )
        
        # Verify Excel file was created
        self.assertTrue(excel_file.exists())
        self.assertGreater(excel_file.stat().st_size, 0)
        self.assertTrue(excel_file.suffix == '.xlsx')
        
        # Verify file is in reports directory
        self.assertTrue(str(output_dir['reports_dir']) in str(excel_file))
    
    def test_docx_export_with_charts(self):
        """Test DOCX export includes embedded charts."""
        output_dir = self.file_utils.create_timestamped_output_directory(self.client_profile)
        
        # Generate test charts
        charts = {}
        
        # Create KPI chart
        kpi_data = {'Project IRR': 12.5, 'Equity IRR': 14.5}
        _, chart_bytes = self.chart_factory.create_and_export_bar_chart(
            data=kpi_data,
            title="Financial KPIs"
        )
        charts['financial_kpis'] = chart_bytes
        
        # Export DOCX report
        docx_file = self.export_service.export_docx_report(
            client_profile=self.client_profile,
            assumptions=self.assumptions,
            financial_results=self.financial_results,
            charts=charts,
            output_dir=output_dir
        )
        
        # Verify DOCX file was created
        self.assertTrue(docx_file.exists())
        self.assertGreater(docx_file.stat().st_size, 0)
        self.assertTrue(docx_file.suffix == '.docx')
        
        # Verify file is in reports directory
        self.assertTrue(str(output_dir['reports_dir']) in str(docx_file))

    def test_html_export_with_embedded_charts(self):
        """Test HTML export includes embedded charts as base64 images."""
        output_dir = self.file_utils.create_timestamped_output_directory(self.client_profile)

        # Generate test charts
        charts = {}

        # Create KPI chart
        kpi_data = {'Project IRR': 12.5, 'Equity IRR': 14.5}
        _, chart_bytes = self.chart_factory.create_and_export_bar_chart(
            data=kpi_data,
            title="Financial KPIs"
        )
        charts['financial_kpis'] = chart_bytes

        # Export HTML report
        html_file = self.export_service.export_html_report(
            client_profile=self.client_profile,
            assumptions=self.assumptions,
            financial_results=self.financial_results,
            charts=charts,
            output_dir=output_dir
        )

        # Verify HTML file was created
        self.assertTrue(html_file.exists())
        self.assertGreater(html_file.stat().st_size, 0)
        self.assertTrue(html_file.suffix == '.html')

        # Verify file is in reports directory
        self.assertTrue(str(output_dir['reports_dir']) in str(html_file))

        # Verify HTML contains embedded charts
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
            self.assertIn('data:image/png;base64,', html_content)
            # Test accepts either "Financial KPIs" or "Financial Kpis" from the chart
            chart_title_present = 'Financial KPIs' in html_content or 'Financial Kpis' in html_content
            self.assertTrue(chart_title_present, "Financial KPIs chart title not found in HTML content")

    def test_json_export_with_complete_data(self):
        """Test JSON export includes all financial data."""
        output_dir = self.file_utils.create_timestamped_output_directory(self.client_profile)

        # Export JSON data
        json_file = self.export_service.export_json_data(
            client_profile=self.client_profile,
            assumptions=self.assumptions,
            financial_results=self.financial_results,
            output_dir=output_dir
        )

        # Verify JSON file was created
        self.assertTrue(json_file.exists())
        self.assertGreater(json_file.stat().st_size, 0)
        self.assertTrue(json_file.suffix == '.json')

        # Verify file is in reports directory
        self.assertTrue(str(output_dir['reports_dir']) in str(json_file))

        # Verify JSON contains expected data
        with open(json_file, 'r', encoding='utf-8') as f:
            json_data = json.load(f)

            self.assertIn('client_profile', json_data)
            self.assertIn('assumptions', json_data)
            self.assertIn('financial_results', json_data)
            self.assertIn('export_timestamp', json_data)
            self.assertIn('version', json_data)

            # Verify financial results structure
            financial_results = json_data['financial_results']
            self.assertIn('kpis', financial_results)
            self.assertIn('cashflow', financial_results)

    def test_comprehensive_report_generation(self):
        """Test comprehensive report generation with all components."""
        # Mock the financial service methods to avoid complex dependencies
        with patch.object(self.report_service.financial_service, 'run_financial_model') as mock_financial, \
             patch.object(self.report_service.validation_service, 'validate_model') as mock_validation, \
             patch.object(self.report_service.validation_service, 'generate_benchmark_comparison') as mock_benchmark, \
             patch.object(self.report_service.location_service, 'compare_locations') as mock_location, \
             patch.object(self.report_service.financial_service, 'run_sensitivity_analysis') as mock_sensitivity, \
             patch.object(self.report_service.financial_service, 'run_monte_carlo_simulation') as mock_monte_carlo, \
             patch.object(self.report_service.financial_service, 'run_scenario_analysis') as mock_scenario:

            # Set up mock returns
            mock_financial.return_value = self.financial_results
            mock_validation.return_value = Mock(is_valid=True, warnings=[], errors=[])
            mock_benchmark.return_value = {'benchmark_data': 'test'}
            mock_location.return_value = {'location_data': 'test'}
            mock_sensitivity.return_value = pd.DataFrame({'Parameter': ['Test'], 'Impact': [0.01]})
            mock_monte_carlo.return_value = {'statistics': {'mean': 0.125}}
            mock_scenario.return_value = {'base_case': {'kpis': self.financial_results['kpis']}}

            # Generate comprehensive report
            results = self.report_service.generate_comprehensive_report(
                client_profile=self.client_profile,
                assumptions=self.assumptions,
                export_formats=['excel', 'docx', 'html']
            )

            # Verify results structure
            self.assertIn('output_directory', results)
            self.assertIn('generated_files', results)
            self.assertIn('analysis_results', results)
            self.assertIn('charts', results)
            self.assertIn('summary', results)

            # Verify files were generated
            self.assertGreater(len(results['generated_files']), 0)

            # Verify all export formats were generated
            file_types = [file_info[0] for file_info in results['generated_files']]
            self.assertIn('Excel Report', file_types)
            self.assertIn('DOCX Report', file_types)
            self.assertIn('HTML Report', file_types)
            self.assertIn('Raw Data (JSON)', file_types)

            # Verify charts were generated
            self.assertIsInstance(results['charts'], dict)

            # Verify output directory structure
            output_dir = results['output_directory']
            for dir_name in ['main_dir', 'data_dir', 'charts_dir', 'reports_dir']:
                self.assertIn(dir_name, output_dir)
                self.assertTrue(output_dir[dir_name].exists())

    def test_export_configuration_settings(self):
        """Test that export configuration settings are properly applied."""
        # Test chart configuration
        chart_settings = self.export_config.get_chart_settings()

        # Verify required settings exist
        required_settings = ['format', 'dpi', 'width', 'height', 'color_scheme', 'enabled_types']
        for setting in required_settings:
            self.assertIn(setting, chart_settings)

        # Test chart type enablement
        self.assertTrue(self.export_config.is_chart_type_enabled('financial_kpis'))
        self.assertTrue(self.export_config.is_chart_type_enabled('dcf_waterfall'))

        # Test color palette
        color_palette = self.export_config.get_color_palette()
        required_colors = ['primary', 'secondary', 'success', 'warning']
        for color in required_colors:
            self.assertIn(color, color_palette)

        # Test chart dimensions by type
        kpi_dimensions = self.export_config.get_chart_dimensions_by_type('financial_kpis')
        self.assertIsInstance(kpi_dimensions, tuple)
        self.assertEqual(len(kpi_dimensions), 2)

        # Test quality settings
        quality_settings = self.export_config.get_export_quality_settings()
        self.assertIn('dpi', quality_settings)
        self.assertIn('compression', quality_settings)

    def test_file_utils_verification(self):
        """Test file utilities verification functions."""
        # Create test directory structure
        directories = self.file_utils.create_timestamped_output_directory(self.client_profile)

        # Verify directory structure
        verification_results = self.file_utils.verify_output_structure(directories)

        # All directories should pass verification
        for dir_name, is_valid in verification_results.items():
            self.assertTrue(is_valid, f"Directory verification failed for {dir_name}")

        # Test file operations
        test_file = directories['data_dir'] / "test_file.txt"
        test_data = {"test": "data"}

        # Test JSON save/load
        json_file = directories['data_dir'] / "test_data.json"
        save_result = self.file_utils.save_json_data(test_data, json_file)
        self.assertTrue(save_result)
        self.assertTrue(json_file.exists())

        loaded_data = self.file_utils.load_json_data(json_file)
        self.assertEqual(loaded_data, test_data)

        # Test file size utilities
        file_size = self.file_utils.get_file_size(json_file)
        self.assertGreater(file_size, 0)

        formatted_size = self.file_utils.format_file_size(file_size)
        self.assertIsInstance(formatted_size, str)
        self.assertIn('B', formatted_size)  # Should contain bytes unit

    def test_error_handling_and_recovery(self):
        """Test error handling in export processes."""
        # Test with invalid output directory
        invalid_output_dir = {
            'main_dir': Path('/invalid/path'),
            'charts_dir': Path('/invalid/path/charts'),
            'reports_dir': Path('/invalid/path/reports')
        }

        # Export should handle invalid paths gracefully
        try:
            # This should not crash but may return empty results
            charts = self.report_service._generate_charts({}, invalid_output_dir)
            self.assertIsInstance(charts, dict)
        except Exception as e:
            # If it does raise an exception, it should be logged appropriately
            self.assertIsInstance(e, Exception)

        # Test with empty financial results
        empty_results = {'kpis': {}, 'cashflow': pd.DataFrame()}

        try:
            charts = self.report_service._generate_charts({'financial': empty_results},
                                                        self.file_utils.create_timestamped_output_directory(self.client_profile))
            self.assertIsInstance(charts, dict)
        except Exception as e:
            # Should handle empty data gracefully
            self.assertIsInstance(e, Exception)


if __name__ == '__main__':
    unittest.main()
