#!/usr/bin/env python3
"""
Debug script to identify IRR calculation issues.
"""

import sys
import os
import numpy as np
import pandas as pd
from typing import Dict, Any

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from services.enhanced_dcf_model import EnhancedDCFModel, EnhancedDCFAssumptions
    print("✅ Successfully imported DCF model")
except ImportError as e:
    print(f"❌ Failed to import DCF model: {e}")
    sys.exit(1)

def test_irr_calculation():
    """Test IRR calculation with various scenarios."""
    print("\n🔍 Testing IRR Calculation Issues")
    print("=" * 50)
    
    # Create model instance
    model = EnhancedDCFModel()
    
    # Test scenarios
    scenarios = [
        {
            "name": "Standard Project",
            "assumptions": EnhancedDCFAssumptions(
                capacity_mw=10.0,
                capex_meur=8.5,
                production_mwh_year1=18000.0,
                ppa_price_eur_kwh=0.045,
                debt_ratio=0.75,
                interest_rate=0.06,
                project_life_years=25
            )
        },
        {
            "name": "High CAPEX Project",
            "assumptions": EnhancedDCFAssumptions(
                capacity_mw=10.0,
                capex_meur=15.0,  # Very high CAPEX
                production_mwh_year1=18000.0,
                ppa_price_eur_kwh=0.045,
                debt_ratio=0.75,
                interest_rate=0.06,
                project_life_years=25
            )
        },
        {
            "name": "Low Revenue Project",
            "assumptions": EnhancedDCFAssumptions(
                capacity_mw=10.0,
                capex_meur=8.5,
                production_mwh_year1=10000.0,  # Low production
                ppa_price_eur_kwh=0.025,  # Low price
                debt_ratio=0.75,
                interest_rate=0.06,
                project_life_years=25
            )
        },
        {
            "name": "No Debt Project",
            "assumptions": EnhancedDCFAssumptions(
                capacity_mw=10.0,
                capex_meur=8.5,
                production_mwh_year1=18000.0,
                ppa_price_eur_kwh=0.045,
                debt_ratio=0.0,  # No debt
                interest_rate=0.06,
                project_life_years=25
            )
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{i}. Testing {scenario['name']}")
        print("-" * 30)
        
        try:
            # Build cashflow
            cashflow_df = model.build_cashflow(scenario['assumptions'])
            print(f"   ✅ Cashflow built successfully ({len(cashflow_df)} years)")
            
            # Check cash flow values
            equity_cf = cashflow_df["Equity_CF"].values
            firm_cf = cashflow_df["Total_FCF_Firm"].values

            print(f"   📊 Equity CF range: {equity_cf.min():.0f} to {equity_cf.max():.0f}")
            print(f"   📊 Firm CF range: {firm_cf.min():.0f} to {firm_cf.max():.0f}")
            print(f"   📊 Initial equity investment: {equity_cf[0]:.0f}")
            print(f"   📊 Initial firm CF: {firm_cf[0]:.0f}")
            print(f"   📊 Sum of positive equity CF: {equity_cf[equity_cf > 0].sum():.0f}")
            print(f"   📊 Sum of positive firm CF: {firm_cf[firm_cf > 0].sum():.0f}")

            # Check for problematic patterns in equity CF
            if all(cf >= 0 for cf in equity_cf):
                print("   ⚠️  WARNING: Equity CF - No negative cash flows (no initial investment)")
            elif all(cf <= 0 for cf in equity_cf):
                print("   ⚠️  WARNING: Equity CF - No positive cash flows (no returns)")
            elif equity_cf[0] >= 0:
                print("   ⚠️  WARNING: Equity CF - Initial cash flow is positive (should be negative investment)")

            # Check for problematic patterns in firm CF
            if all(cf >= 0 for cf in firm_cf):
                print("   ⚠️  WARNING: Firm CF - No negative cash flows (no initial investment)")
            elif all(cf <= 0 for cf in firm_cf):
                print("   ⚠️  WARNING: Firm CF - No positive cash flows (no returns)")
            elif firm_cf[0] >= 0:
                print("   ⚠️  WARNING: Firm CF - Initial cash flow is positive (should be negative investment)")

            # Show first few cash flows for debugging
            print(f"   🔍 First 5 Equity CF: {equity_cf[:5]}")
            print(f"   🔍 First 5 Firm CF: {firm_cf[:5]}")
            
            # Compute KPIs
            kpis = model.compute_kpis(cashflow_df, scenario['assumptions'])
            
            irr_equity = kpis.get('IRR_equity', np.nan)
            irr_project = kpis.get('IRR_project', np.nan)
            
            if np.isnan(irr_equity):
                print("   ❌ IRR Equity: FAILED (NaN)")
            else:
                print(f"   ✅ IRR Equity: {irr_equity:.2%}")
                
            if np.isnan(irr_project):
                print("   ❌ IRR Project: FAILED (NaN)")
            else:
                print(f"   ✅ IRR Project: {irr_project:.2%}")
            
            # Test manual IRR calculation
            print("   🔧 Testing manual IRR calculation...")
            manual_irr = test_manual_irr(equity_cf)
            if manual_irr is not None:
                print(f"   ✅ Manual IRR: {manual_irr:.2%}")
            else:
                print("   ❌ Manual IRR: FAILED")
                
        except Exception as e:
            print(f"   ❌ ERROR: {str(e)}")
            import traceback
            print(f"   📋 Traceback: {traceback.format_exc()}")

def test_manual_irr(cash_flows):
    """Test manual IRR calculation using Newton-Raphson method."""
    try:
        # Check if we have valid cash flows for IRR
        if len(cash_flows) < 2:
            return None
            
        # Check if we have both positive and negative cash flows
        has_negative = any(cf < 0 for cf in cash_flows)
        has_positive = any(cf > 0 for cf in cash_flows)
        
        if not (has_negative and has_positive):
            return None
            
        # Newton-Raphson method
        rate = 0.1  # Initial guess
        tolerance = 1e-6
        max_iterations = 100
        
        for iteration in range(max_iterations):
            # Calculate NPV and its derivative
            npv = sum(cf / (1 + rate) ** i for i, cf in enumerate(cash_flows))
            dnpv = sum(-i * cf / (1 + rate) ** (i + 1) for i, cf in enumerate(cash_flows))
            
            if abs(dnpv) < 1e-10:  # Avoid division by zero
                break
                
            new_rate = rate - npv / dnpv
            
            if abs(new_rate - rate) < tolerance:
                return new_rate
                
            rate = new_rate
            
        return rate if abs(npv) < tolerance else None
        
    except Exception as e:
        print(f"   ⚠️  Manual IRR calculation error: {e}")
        return None

def test_numpy_financial():
    """Test numpy_financial availability and functionality."""
    print("\n🔍 Testing numpy_financial Library")
    print("=" * 40)
    
    try:
        import numpy_financial as npf
        print("✅ numpy_financial imported successfully")
        
        # Test with simple cash flows
        test_cf = [-1000, 300, 300, 300, 300, 300]
        irr_result = npf.irr(test_cf)
        print(f"✅ Test IRR calculation: {irr_result:.2%}")
        
    except ImportError:
        print("❌ numpy_financial not available, using fallback")
        
        # Test fallback implementation
        from services.enhanced_dcf_model import npf
        test_cf = [-1000, 300, 300, 300, 300, 300]
        irr_result = npf.irr(test_cf)
        print(f"✅ Fallback IRR calculation: {irr_result:.2%}")
        
    except Exception as e:
        print(f"❌ Error testing numpy_financial: {e}")

if __name__ == "__main__":
    print("🚀 Starting IRR Debug Analysis")
    print("=" * 60)
    
    # Test numpy_financial first
    test_numpy_financial()
    
    # Test IRR calculations
    test_irr_calculation()
    
    print("\n✅ IRR Debug Analysis Complete")
