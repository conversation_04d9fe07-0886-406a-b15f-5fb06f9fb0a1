"""
Test Models
===========

Unit tests for data models.
"""

import unittest
from datetime import datetime

from ..models.client_profile import ClientProfile
from ..models.project_assumptions import EnhancedProjectAssumptions


class TestClientProfile(unittest.TestCase):
    """Test cases for ClientProfile model."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.client_profile = ClientProfile()
    
    def test_default_values(self):
        """Test default values are set correctly."""
        self.assertEqual(self.client_profile.company_name, "")
        self.assertEqual(self.client_profile.preferred_currency, "EUR")
        self.assertIsNotNone(self.client_profile.report_date)
    
    def test_validation_empty_profile(self):
        """Test validation of empty profile."""
        errors = self.client_profile.validate()
        self.assertIn('company_name', errors)
        self.assertIn('client_name', errors)
        self.assertIn('project_name', errors)
    
    def test_validation_complete_profile(self):
        """Test validation of complete profile."""
        self.client_profile.company_name = "Test Company"
        self.client_profile.client_name = "<PERSON>"
        self.client_profile.project_name = "Test Project"
        self.client_profile.contact_email = "<EMAIL>"
        
        errors = self.client_profile.validate()
        self.assertEqual(len(errors), 0)
    
    def test_invalid_email(self):
        """Test validation of invalid email."""
        self.client_profile.contact_email = "invalid-email"
        errors = self.client_profile.validate()
        self.assertIn('contact_email', errors)
    
    def test_clean_company_name(self):
        """Test clean company name generation."""
        self.client_profile.company_name = "Test Company & Co."
        clean_name = self.client_profile.get_clean_company_name()
        self.assertEqual(clean_name, "Test_Company_Co")
    
    def test_to_dict_from_dict(self):
        """Test serialization and deserialization."""
        self.client_profile.company_name = "Test Company"
        self.client_profile.client_name = "John Doe"
        
        data = self.client_profile.to_dict()
        new_profile = ClientProfile.from_dict(data)
        
        self.assertEqual(new_profile.company_name, "Test Company")
        self.assertEqual(new_profile.client_name, "John Doe")


class TestEnhancedProjectAssumptions(unittest.TestCase):
    """Test cases for EnhancedProjectAssumptions model."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.assumptions = EnhancedProjectAssumptions()
    
    def test_default_values(self):
        """Test default values are set correctly."""
        self.assertEqual(self.assumptions.capacity_mw, 10.0)
        self.assertEqual(self.assumptions.project_life_years, 25)
        self.assertEqual(self.assumptions.debt_ratio, 0.75)
    
    def test_validation_default_assumptions(self):
        """Test validation of default assumptions."""
        errors = self.assumptions.validate_all()
        # Default values should be valid
        self.assertEqual(len(errors), 0)
        self.assertTrue(self.assumptions.is_validated)
    
    def test_validation_invalid_capacity(self):
        """Test validation of invalid capacity."""
        self.assumptions.capacity_mw = -5.0
        errors = self.assumptions.validate_all()
        self.assertIn('capacity_mw', errors)
        self.assertFalse(self.assumptions.is_validated)
    
    def test_validation_invalid_debt_ratio(self):
        """Test validation of invalid debt ratio."""
        self.assumptions.debt_ratio = 1.5  # > 1
        errors = self.assumptions.validate_all()
        self.assertIn('debt_ratio', errors)
        self.assertFalse(self.assumptions.is_validated)
    
    def test_calculate_capacity_factor(self):
        """Test capacity factor calculation."""
        self.assumptions.capacity_mw = 10.0
        self.assumptions.production_mwh_year1 = 18000.0
        
        cf = self.assumptions.calculate_capacity_factor()
        expected_cf = 18000.0 / (10.0 * 8760)
        self.assertAlmostEqual(cf, expected_cf, places=4)
    
    def test_calculate_total_grants(self):
        """Test total grants calculation."""
        self.assumptions.grant_meur_italy = 1.0
        self.assumptions.grant_meur_masen = 0.5
        self.assumptions.grant_meur_connection = 0.3
        
        total = self.assumptions.calculate_total_grants()
        self.assertEqual(total, 1.8)
    
    def test_calculate_grant_percentage(self):
        """Test grant percentage calculation."""
        self.assumptions.capex_meur = 10.0
        self.assumptions.grant_meur_italy = 2.0
        
        percentage = self.assumptions.calculate_grant_percentage()
        self.assertEqual(percentage, 20.0)
    
    def test_financial_summary(self):
        """Test financial summary generation."""
        summary = self.assumptions.get_financial_summary()
        
        self.assertIn('capacity_mw', summary)
        self.assertIn('capex_meur', summary)
        self.assertIn('specific_capex_eur_kw', summary)
        self.assertIn('capacity_factor', summary)
    
    def test_copy_with_modifications(self):
        """Test copying with modifications."""
        original_capacity = self.assumptions.capacity_mw
        
        modified = self.assumptions.copy_with_modifications(capacity_mw=20.0)
        
        self.assertEqual(self.assumptions.capacity_mw, original_capacity)
        self.assertEqual(modified.capacity_mw, 20.0)


if __name__ == '__main__':
    unittest.main()
