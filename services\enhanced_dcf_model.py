"""
Enhanced DCF Financial Model - 2025 Edition
==========================================

Comprehensive DCF modeling with modern financial analysis features.
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import logging

try:
    import numpy_financial as npf
except ImportError:
    # Fallback implementation
    def _npv(rate, values):
        return sum(v / (1 + rate) ** i for i, v in enumerate(values))

    def _irr(values, tol=1e-6, maxiter=100):
        """Fallback IRR calculation using Newton-Raphson method."""
        # Validate inputs
        if len(values) < 2:
            return np.nan

        # Check if we have both positive and negative cash flows
        has_negative = any(v < 0 for v in values)
        has_positive = any(v > 0 for v in values)

        if not (has_negative and has_positive):
            return np.nan

        rate = 0.1  # Initial guess
        for iteration in range(maxiter):
            try:
                f = _npv(rate, values)
                df = sum(-i * v / (1 + rate) ** (i + 1) for i, v in enumerate(values))

                if abs(df) < 1e-10:  # Avoid division by zero
                    break

                rate_new = rate - f / df

                # Prevent extreme values
                if rate_new < -0.99:  # Prevent rates below -99%
                    rate_new = -0.99
                elif rate_new > 10.0:  # Prevent rates above 1000%
                    rate_new = 10.0

                if abs(rate_new - rate) < tol:
                    return rate_new

                rate = rate_new

            except (ZeroDivisionError, OverflowError, ValueError):
                return np.nan

        # Final validation
        if abs(_npv(rate, values)) < tol:
            return rate
        return np.nan

    class npf:
        irr = staticmethod(_irr)
        npv = staticmethod(_npv)


@dataclass
class EnhancedDCFAssumptions:
    """Enhanced DCF assumptions for 2025 modeling standards."""
    
    # Basic project parameters
    capacity_mw: float = 10.0
    production_mwh_year1: float = 18000.0
    project_life_years: int = 25
    
    # Financial parameters
    capex_meur: float = 8.5
    opex_keuros_year1: float = 180.0
    ppa_price_eur_kwh: float = 0.045
    ppa_escalation: float = 0.0
    opex_escalation: float = 0.02
    
    # Financing structure
    debt_ratio: float = 0.75
    interest_rate: float = 0.06
    debt_years: int = 15
    grace_years: int = 2
    discount_rate: float = 0.08
    tax_rate: float = 0.30
    tax_holiday: int = 0
    
    # Degradation and performance
    degradation_year1: float = 0.025
    degradation_annual: float = 0.005
    
    # Enhanced parameters for 2025
    terminal_growth_rate: float = 0.025
    terminal_value_method: str = "perpetuity"  # or "exit_multiple"
    exit_multiple_ebitda: float = 8.0
    use_terminal_value: bool = True
    
    # Working capital and additional costs
    working_capital_days: int = 30
    insurance_rate: float = 0.002
    land_lease_eur_mw_year: float = 1500.0
    
    # Grants
    total_grants_meur: float = 0.0
    
    @property
    def investment_for_debt_sizing_meur(self) -> float:
        """Calculate investment amount for debt sizing."""
        return max(0, self.capex_meur - self.total_grants_meur)


class EnhancedDCFModel:
    """Enhanced DCF Model with 2025 standards and comprehensive analysis."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def build_cashflow(self, assumptions: EnhancedDCFAssumptions) -> pd.DataFrame:
        """Build comprehensive DCF cashflow model."""
        years = np.arange(0, assumptions.project_life_years + 1)
        df = pd.DataFrame(index=years)
        
        # CAPEX and Grants (Year 0)
        df["Capex"] = 0.0
        df.loc[0, "Capex"] = -assumptions.capex_meur * 1e6
        df["Grants"] = 0.0
        df.loc[0, "Grants"] = assumptions.total_grants_meur * 1e6
        
        # Production with enhanced degradation modeling
        production = self._calculate_production(assumptions, years)
        df["Production_MWh"] = production
        
        # Revenue with price escalation
        prices = self._calculate_prices(assumptions, years)
        df["Price_EUR_kWh"] = prices
        df["Revenue"] = df["Production_MWh"] * 1000 * df["Price_EUR_kWh"]
        
        # Enhanced OPEX modeling
        opex_components = self._calculate_opex(assumptions, years)
        df["Base_OPEX"] = opex_components['base']
        df["Insurance"] = opex_components['insurance']
        df["Land_Lease"] = opex_components['land_lease']
        df["Total_OPEX"] = df["Base_OPEX"] + df["Insurance"] + df["Land_Lease"]
        
        # Working capital changes
        wc_changes = self._calculate_working_capital_changes(df, assumptions)
        df["WC_Change"] = wc_changes
        
        # EBITDA
        df["EBITDA"] = df["Revenue"] - df["Total_OPEX"]
        
        # Debt schedule
        debt_schedule = self._calculate_debt_schedule(assumptions, years)
        df["Interest_Expense"] = debt_schedule['interest']
        df["Principal_Repayment"] = debt_schedule['principal']
        df["Debt_Service"] = df["Interest_Expense"] + df["Principal_Repayment"]
        
        # Taxes
        df["Taxable_Income"] = df["EBITDA"] + df["Interest_Expense"]  # Interest is negative
        df["Tax"] = self._calculate_taxes(df["Taxable_Income"], assumptions, years)
        
        # Free Cash Flow to Firm
        df["FCF_Firm"] = df["EBITDA"] + df["Interest_Expense"] + df["Tax"] - df["WC_Change"]

        # Terminal Value
        if assumptions.use_terminal_value:
            terminal_value = self._calculate_terminal_value(df, assumptions)
            df["Terminal_Value"] = 0.0
            df.loc[assumptions.project_life_years, "Terminal_Value"] = terminal_value
        else:
            df["Terminal_Value"] = 0.0

        # Total FCF including terminal value and initial investment for Project IRR
        df["Total_FCF_Firm"] = df["FCF_Firm"] + df["Terminal_Value"]
        # Add initial CAPEX (net of grants) to year 0 for proper Project IRR calculation
        df.loc[0, "Total_FCF_Firm"] += df.loc[0, "Capex"] + df.loc[0, "Grants"]
        
        # Equity Cash Flow
        debt_drawdown = assumptions.investment_for_debt_sizing_meur * 1e6 * assumptions.debt_ratio
        # Start with FCF_Firm (before adding CAPEX) for equity calculation
        df["Equity_CF"] = df["FCF_Firm"] + df["Terminal_Value"] - df["Principal_Repayment"]
        # Add equity portion of initial investment (CAPEX net of debt and grants)
        df.loc[0, "Equity_CF"] += df.loc[0, "Capex"] - debt_drawdown + df.loc[0, "Grants"]
        
        # DSCR
        df["DSCR"] = df["EBITDA"] / df["Debt_Service"].replace(0, np.nan)
        df["DSCR"] = df["DSCR"].replace([np.inf, -np.inf], np.nan)
        
        return df.fillna(0)
    
    def _calculate_production(self, assumptions: EnhancedDCFAssumptions, years: np.ndarray) -> List[float]:
        """Calculate production with enhanced degradation modeling."""
        production = [0.0]  # Year 0
        
        for year in years[1:]:
            if year == 1:
                prod = assumptions.production_mwh_year1 * (1 - assumptions.degradation_year1)
            else:
                prod = production[-1] * (1 - assumptions.degradation_annual)
            production.append(prod)
        
        return production
    
    def _calculate_prices(self, assumptions: EnhancedDCFAssumptions, years: np.ndarray) -> List[float]:
        """Calculate prices with escalation."""
        prices = [0.0]  # Year 0
        
        for year in years[1:]:
            price = assumptions.ppa_price_eur_kwh * (1 + assumptions.ppa_escalation) ** (year - 1)
            prices.append(price)
        
        return prices
    
    def _calculate_opex(self, assumptions: EnhancedDCFAssumptions, years: np.ndarray) -> Dict[str, List[float]]:
        """Calculate OPEX components."""
        base_opex = [0.0]
        insurance = [0.0]
        land_lease = [0.0]
        
        for year in years[1:]:
            # Base OPEX with escalation
            base = assumptions.opex_keuros_year1 * 1000 * (1 + assumptions.opex_escalation) ** (year - 1)
            base_opex.append(-base)  # Negative for cash outflow
            
            # Insurance as percentage of CAPEX
            ins = assumptions.capex_meur * 1e6 * assumptions.insurance_rate
            insurance.append(-ins)
            
            # Land lease
            lease = assumptions.capacity_mw * assumptions.land_lease_eur_mw_year
            land_lease.append(-lease)
        
        return {
            'base': base_opex,
            'insurance': insurance,
            'land_lease': land_lease
        }
    
    def _calculate_working_capital_changes(self, df: pd.DataFrame, assumptions: EnhancedDCFAssumptions) -> List[float]:
        """Calculate working capital changes."""
        wc = df["Revenue"] * assumptions.working_capital_days / 365
        wc_changes = wc.diff().fillna(wc.iloc[0] if len(wc) > 0 else 0)
        return (-wc_changes).tolist()  # Negative because increase in WC is cash outflow
    
    def _calculate_debt_schedule(self, assumptions: EnhancedDCFAssumptions, years: np.ndarray) -> Dict[str, List[float]]:
        """Calculate debt schedule with grace period."""
        debt_amount = assumptions.investment_for_debt_sizing_meur * 1e6 * assumptions.debt_ratio
        debt_amount = max(0, debt_amount)
        
        repayment_years = assumptions.debt_years - assumptions.grace_years
        annual_principal = debt_amount / repayment_years if repayment_years > 0 else 0
        
        outstanding_debt = debt_amount
        interest_payments = [0.0]  # Year 0
        principal_payments = [0.0]  # Year 0
        
        for year in years[1:]:
            # Interest payment
            interest = -outstanding_debt * assumptions.interest_rate if outstanding_debt > 0 else 0.0
            interest_payments.append(interest)
            
            # Principal payment
            if year <= assumptions.grace_years:
                principal = 0.0
            else:
                principal = -min(annual_principal, outstanding_debt)
            
            principal_payments.append(principal)
            outstanding_debt += principal  # Principal is negative
        
        return {
            'interest': interest_payments,
            'principal': principal_payments
        }
    
    def _calculate_taxes(self, taxable_income: pd.Series, assumptions: EnhancedDCFAssumptions, years: np.ndarray) -> List[float]:
        """Calculate taxes with holiday period."""
        taxes = []
        
        for year, income in zip(years, taxable_income):
            if year <= assumptions.tax_holiday:
                tax = 0.0
            else:
                tax = -max(0, income * assumptions.tax_rate)
            taxes.append(tax)
        
        return taxes
    
    def _calculate_terminal_value(self, df: pd.DataFrame, assumptions: EnhancedDCFAssumptions) -> float:
        """Calculate terminal value using specified method."""
        if assumptions.terminal_value_method == "perpetuity":
            final_fcf = df.loc[assumptions.project_life_years, "FCF_Firm"]
            if assumptions.discount_rate <= assumptions.terminal_growth_rate:
                return 0.0  # Avoid negative or infinite values
            
            terminal_value = (final_fcf * (1 + assumptions.terminal_growth_rate)) / \
                           (assumptions.discount_rate - assumptions.terminal_growth_rate)
            return max(0, terminal_value)
        
        elif assumptions.terminal_value_method == "exit_multiple":
            final_ebitda = df.loc[assumptions.project_life_years, "EBITDA"]
            return max(0, final_ebitda * assumptions.exit_multiple_ebitda)
        
        return 0.0
    
    def compute_kpis(self, cashflow_df: pd.DataFrame, assumptions: EnhancedDCFAssumptions) -> Dict[str, float]:
        """Compute comprehensive KPIs including enhanced DCF metrics."""
        equity_cf = cashflow_df["Equity_CF"].values
        firm_cf = cashflow_df["Total_FCF_Firm"].values
        
        # IRR calculations with enhanced error handling
        try:
            irr_equity = npf.irr(equity_cf)
            if np.isnan(irr_equity) or np.isinf(irr_equity):
                self.logger.warning("Equity IRR calculation returned NaN/Inf")
                irr_equity = np.nan
        except Exception as e:
            self.logger.warning(f"Equity IRR calculation failed: {e}")
            irr_equity = np.nan

        try:
            irr_project = npf.irr(firm_cf)
            if np.isnan(irr_project) or np.isinf(irr_project):
                self.logger.warning("Project IRR calculation returned NaN/Inf")
                irr_project = np.nan
        except Exception as e:
            self.logger.warning(f"Project IRR calculation failed: {e}")
            irr_project = np.nan
        
        # NPV calculations
        npv_equity = npf.npv(assumptions.discount_rate, equity_cf)
        npv_project = npf.npv(assumptions.discount_rate, firm_cf)
        
        # LCOE calculation
        lcoe = self._calculate_lcoe(cashflow_df, assumptions)
        
        # Financial ratios
        min_dscr = cashflow_df["DSCR"].replace([np.inf, -np.inf], np.nan).min()
        avg_dscr = cashflow_df["DSCR"].replace([np.inf, -np.inf], np.nan).mean()
        
        # Payback period
        payback_years = self._calculate_payback_period(equity_cf)
        
        # Additional 2025 metrics
        debt_to_equity = assumptions.debt_ratio / (1 - assumptions.debt_ratio)
        grant_percentage = (assumptions.total_grants_meur / assumptions.capex_meur) * 100
        
        return {
            "IRR_equity": irr_equity,
            "IRR_project": irr_project,
            "NPV_equity": npv_equity,
            "NPV_project": npv_project,
            "LCOE_eur_kwh": lcoe,
            "Min_DSCR": min_dscr,
            "Avg_DSCR": avg_dscr,
            "Payback_years": payback_years,
            "Debt_to_equity_ratio": debt_to_equity,
            "Grant_percentage": grant_percentage,
            "Terminal_value": cashflow_df.loc[assumptions.project_life_years, "Terminal_Value"],
            "Total_revenue": cashflow_df["Revenue"].sum(),
            "Total_opex": abs(cashflow_df["Total_OPEX"].sum()),
            "Total_capex": abs(cashflow_df["Capex"].sum())
        }
    
    def _calculate_lcoe(self, df: pd.DataFrame, assumptions: EnhancedDCFAssumptions) -> float:
        """Calculate Levelized Cost of Energy."""
        discount_factors = (1 / (1 + assumptions.discount_rate) ** df.index).values
        
        # Total costs (excluding grants)
        total_costs = -(df["Capex"] + df["Total_OPEX"]).values
        discounted_costs = (total_costs * discount_factors).sum()
        
        # Total energy production
        total_energy_kwh = (df["Production_MWh"] * 1000).values
        discounted_energy = (total_energy_kwh * discount_factors).sum()
        
        return discounted_costs / discounted_energy if discounted_energy > 0 else np.nan
    
    def _calculate_payback_period(self, cash_flows: np.ndarray) -> float:
        """Calculate simple payback period."""
        cumulative_cf = cash_flows.cumsum()
        positive_indices = np.where(cumulative_cf > 0)[0]
        
        if len(positive_indices) > 0:
            return float(positive_indices[0])
        return np.nan
