# Enhanced Financial Model Application v2.0

A comprehensive financial modeling application for solar PV projects with advanced analysis capabilities.

## Author & Company Information

**Author:** <PERSON><PERSON><PERSON><PERSON>  
**Company:** Agevolami SRL  
**Website:** www.agevolami.it & www.agevolami.ma  
**Tagline:** Your way to explore crossborder opportunities and grow big  

## Features

### Core Functionality
- **Project Setup & Configuration**: Complete client profile and project parameter management
- **Financial Modeling**: Advanced DCF analysis with comprehensive KPI calculations
- **Location Comparison**: Multi-location analysis for optimal project placement
- **Validation & Benchmarking**: Industry benchmark comparison and model validation
- **Sensitivity Analysis**: Impact analysis of key variables on project returns
- **Monte Carlo Simulation**: Risk analysis through probabilistic modeling
- **Scenario Analysis**: Multiple scenario comparison (Base, Optimistic, Pessimistic)
- **Professional Reporting**: Export to Excel, DOCX, HTML, and JSON formats

### Enhanced Features
- **Security Screen**: Password-protected access with developer information
- **Comprehensive Analysis**: One-click generation of all analyses and reports
- **LCOE Impact Breakdown**: Detailed analysis of Italian grants, Moroccan grants, VAT reduction, and tax exemptions
- **Professional Charts**: Interactive visualizations and export-ready charts
- **Modular Architecture**: Clean separation of concerns for maintainability

## Installation

1. **Clone or download the application**
2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

## Usage

### Running the Application
```bash
python src/new_app/main.py
```

### Security Access
- Default password: `agevolami2024`
- The security screen displays developer and company information

### Application Workflow
1. **Project Setup**: Enter client information and project parameters
2. **Run Analysis**: Use "Generate Complete Analysis & Reports" for comprehensive analysis
3. **Review Results**: Navigate through different tabs to review analysis results
4. **Export Reports**: Generate professional reports in multiple formats

## Application Structure

```
src/new_app/
├── app/                    # Application core
│   ├── app_controller.py   # Main application controller
│   └── app_state.py        # Centralized state management
├── models/                 # Data models
│   ├── client_profile.py   # Client information model
│   ├── project_assumptions.py # Project parameters model
│   ├── location_config.py  # Location configuration
│   └── ui_state.py         # UI state management
├── services/               # Business logic services
│   ├── financial_service.py # Financial modeling service
│   ├── validation_service.py # Model validation service
│   ├── export_service.py   # Export functionality
│   ├── location_service.py # Location comparison service
│   └── report_service.py   # Report generation service
├── views/                  # UI view components
│   ├── base_view.py        # Base view class
│   ├── project_setup_view.py # Project setup interface
│   ├── dashboard_view.py   # Main dashboard
│   ├── location_comparison_view.py # Location comparison
│   ├── financial_model_view.py # Financial results
│   ├── validation_view.py  # Validation results
│   ├── sensitivity_view.py # Sensitivity analysis
│   ├── monte_carlo_view.py # Monte Carlo simulation
│   ├── scenarios_view.py   # Scenario analysis
│   └── export_view.py      # Export interface
├── components/             # Reusable UI components
│   ├── charts/             # Chart components
│   ├── forms/              # Form components
│   └── widgets/            # UI widgets
├── config/                 # Configuration
│   ├── app_config.py       # Application configuration
│   ├── ui_config.py        # UI configuration
│   └── export_config.py    # Export configuration
├── utils/                  # Utility functions
│   ├── file_utils.py       # File operations
│   ├── validation_utils.py # Data validation
│   └── formatting_utils.py # Data formatting
└── main.py                 # Application entry point
```

## Key Features Detail

### Financial Analysis
- **IRR Calculation**: Project and equity IRR with industry benchmarks
- **NPV Analysis**: Net present value calculations
- **LCOE Calculation**: Levelized cost of energy
- **DSCR Analysis**: Debt service coverage ratio timeline
- **Payback Period**: Investment recovery analysis

### Grant Analysis
- **Italian Government Grants**: Integration with Italian incentive schemes
- **MASEN Strategic Grants**: Moroccan renewable energy grants
- **Grid Connection Grants**: Infrastructure support grants
- **SIMEST African Fund**: International development funding
- **Impact Analysis**: Detailed breakdown of grant impact on LCOE

### Location Comparison
- **Multi-location Analysis**: Compare up to 9 Moroccan locations
- **Ranking System**: Automatic ranking by different criteria
- **Risk Assessment**: Location-specific risk analysis
- **Recommendations**: AI-powered location recommendations

### Export Capabilities
- **Excel Reports**: Comprehensive financial models with charts
- **DOCX Reports**: Professional documents with embedded charts
- **HTML Reports**: Web-ready reports with responsive design
- **JSON Data**: Raw data export for integration

## Technical Requirements

- **Python**: 3.8 or higher
- **Flet**: 0.21.0 or higher
- **Pandas**: For data processing
- **Matplotlib**: For chart generation
- **OpenPyXL**: For Excel export
- **Python-DOCX**: For Word document export

## Configuration

The application uses configuration files for customization:
- `app_config.py`: General application settings
- `ui_config.py`: User interface configuration
- `export_config.py`: Export format settings

## Support & Contact

For support, customization, or business inquiries:

**Abdelhalim Serhani**  
Financial & Business Consultant  
Agevolami SRL  
Email: Available through www.agevolami.it  
Websites: www.agevolami.it & www.agevolami.ma  

*"Your way to explore crossborder opportunities and grow big"*

## License

This application is proprietary software developed by Agevolami SRL.
All rights reserved.

## Version History

- **v2.0.0**: Complete modular refactor with enhanced features
- **v1.0.0**: Initial monolithic version
