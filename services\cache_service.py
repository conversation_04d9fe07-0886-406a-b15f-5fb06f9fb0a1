"""
Performance Caching Service
===========================

High-performance caching with LRU, TTL, and optional Redis support.
"""

import time
import hashlib
import pickle
import logging
import threading
from typing import Any, Optional, Dict, Callable, Union, <PERSON><PERSON>
from datetime import datetime, timedelta
from functools import wraps
import json

# Optional Redis support
try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False


class LRUCache:
    """Thread-safe LRU cache with TTL support."""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 3600):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache = {}
        self.access_order = []
        self.lock = threading.RLock()
        
        # Statistics
        self.hits = 0
        self.misses = 0
        self.evictions = 0
        
    def get(self, key: str) -> Tuple[Any, bool]:
        """Get value from cache. Returns (value, found)."""
        with self.lock:
            if key in self.cache:
                entry = self.cache[key]
                
                # Check TTL
                if entry['expires_at'] and datetime.now() > entry['expires_at']:
                    self._remove_key(key)
                    self.misses += 1
                    return None, False
                
                # Update access order
                self.access_order.remove(key)
                self.access_order.append(key)
                
                self.hits += 1
                return entry['value'], True
            
            self.misses += 1
            return None, False
    
    def put(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Put value in cache with optional TTL."""
        with self.lock:
            # Calculate expiration
            expires_at = None
            if ttl is None:
                ttl = self.default_ttl
            if ttl > 0:
                expires_at = datetime.now() + timedelta(seconds=ttl)
            
            # Remove if already exists
            if key in self.cache:
                self.access_order.remove(key)
            
            # Check size limit
            while len(self.cache) >= self.max_size:
                self._evict_lru()
            
            # Add new entry
            self.cache[key] = {
                'value': value,
                'expires_at': expires_at,
                'created_at': datetime.now()
            }
            self.access_order.append(key)
    
    def remove(self, key: str) -> bool:
        """Remove key from cache."""
        with self.lock:
            if key in self.cache:
                self._remove_key(key)
                return True
            return False
    
    def clear(self) -> None:
        """Clear all cache entries."""
        with self.lock:
            self.cache.clear()
            self.access_order.clear()
    
    def _remove_key(self, key: str) -> None:
        """Remove key from cache (internal)."""
        if key in self.cache:
            del self.cache[key]
            self.access_order.remove(key)
    
    def _evict_lru(self) -> None:
        """Evict least recently used item."""
        if self.access_order:
            lru_key = self.access_order[0]
            self._remove_key(lru_key)
            self.evictions += 1
    
    def stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self.lock:
            total_requests = self.hits + self.misses
            hit_rate = (self.hits / total_requests) if total_requests > 0 else 0
            
            return {
                'hits': self.hits,
                'misses': self.misses,
                'evictions': self.evictions,
                'hit_rate': hit_rate,
                'size': len(self.cache),
                'max_size': self.max_size
            }
    
    def cleanup_expired(self) -> int:
        """Remove expired entries. Returns count of removed entries."""
        with self.lock:
            now = datetime.now()
            expired_keys = []
            
            for key, entry in self.cache.items():
                if entry['expires_at'] and now > entry['expires_at']:
                    expired_keys.append(key)
            
            for key in expired_keys:
                self._remove_key(key)
            
            return len(expired_keys)


class RedisCache:
    """Redis-based cache with JSON serialization."""
    
    def __init__(self, host: str = 'localhost', port: int = 6379, db: int = 0, 
                 default_ttl: int = 3600, key_prefix: str = 'hiel_cache:'):
        if not REDIS_AVAILABLE:
            raise ImportError("Redis package not available")
        
        self.default_ttl = default_ttl
        self.key_prefix = key_prefix
        self.redis_client = redis.Redis(host=host, port=port, db=db, decode_responses=True)
        
        # Test connection
        try:
            self.redis_client.ping()
        except Exception as e:
            raise ConnectionError(f"Failed to connect to Redis: {e}")
    
    def get(self, key: str) -> Tuple[Any, bool]:
        """Get value from Redis cache."""
        try:
            value = self.redis_client.get(self._make_key(key))
            if value is not None:
                return json.loads(value), True
            return None, False
        except Exception:
            return None, False
    
    def put(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Put value in Redis cache."""
        try:
            redis_key = self._make_key(key)
            serialized_value = json.dumps(value, default=str)
            
            if ttl is None:
                ttl = self.default_ttl
            
            if ttl > 0:
                self.redis_client.setex(redis_key, ttl, serialized_value)
            else:
                self.redis_client.set(redis_key, serialized_value)
        except Exception as e:
            # Fail silently for cache errors
            pass
    
    def remove(self, key: str) -> bool:
        """Remove key from Redis cache."""
        try:
            return bool(self.redis_client.delete(self._make_key(key)))
        except Exception:
            return False
    
    def clear(self, pattern: str = None) -> None:
        """Clear cache entries matching pattern."""
        try:
            if pattern:
                keys = self.redis_client.keys(self._make_key(pattern))
            else:
                keys = self.redis_client.keys(self._make_key('*'))
            
            if keys:
                self.redis_client.delete(*keys)
        except Exception:
            pass
    
    def _make_key(self, key: str) -> str:
        """Create Redis key with prefix."""
        return f"{self.key_prefix}{key}"


class PerformanceCacheService:
    """High-performance caching service with multiple backend support."""
    
    def __init__(self, use_redis: bool = False, redis_config: Optional[Dict] = None,
                 lru_max_size: int = 1000, default_ttl: int = 3600):
        self.logger = logging.getLogger(__name__)
        self.default_ttl = default_ttl
        
        # Initialize cache backend
        if use_redis and REDIS_AVAILABLE:
            try:
                redis_config = redis_config or {}
                self.cache = RedisCache(default_ttl=default_ttl, **redis_config)
                self.backend_type = "redis"
                self.logger.info("Initialized Redis cache backend")
            except Exception as e:
                self.logger.warning(f"Failed to initialize Redis, falling back to LRU: {e}")
                self.cache = LRUCache(max_size=lru_max_size, default_ttl=default_ttl)
                self.backend_type = "lru"
        else:
            self.cache = LRUCache(max_size=lru_max_size, default_ttl=default_ttl)
            self.backend_type = "lru"
            self.logger.info("Initialized LRU cache backend")
        
        # Cache statistics
        self.stats_lock = threading.Lock()
        self.operation_stats = {
            'get_operations': 0,
            'put_operations': 0,
            'remove_operations': 0,
            'clear_operations': 0
        }
    
    def get(self, key: str) -> Tuple[Any, bool]:
        """Get value from cache."""
        with self.stats_lock:
            self.operation_stats['get_operations'] += 1
        
        return self.cache.get(key)
    
    def put(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Put value in cache."""
        with self.stats_lock:
            self.operation_stats['put_operations'] += 1
        
        self.cache.put(key, value, ttl)
    
    def remove(self, key: str) -> bool:
        """Remove key from cache."""
        with self.stats_lock:
            self.operation_stats['remove_operations'] += 1
        
        return self.cache.remove(key)
    
    def invalidate(self, key: str) -> bool:
        """Invalidate (remove) key from cache. Alias for remove."""
        return self.remove(key)
    
    def clear(self, pattern: str = None) -> None:
        """Clear cache entries."""
        with self.stats_lock:
            self.operation_stats['clear_operations'] += 1
        
        if hasattr(self.cache, 'clear'):
            if pattern and hasattr(self.cache, '_make_key'):
                self.cache.clear(pattern)
            else:
                self.cache.clear()
    
    def clear_all(self) -> None:
        """Clear all cache entries."""
        self.clear()
    
    def invalidate_prefix(self, prefix: str) -> None:
        """Invalidate all keys with given prefix."""
        if self.backend_type == "redis":
            self.clear(f"{prefix}*")
        else:
            # For LRU cache, we need to manually find and remove keys
            keys_to_remove = []
            with self.cache.lock:
                for key in self.cache.cache.keys():
                    if key.startswith(prefix):
                        keys_to_remove.append(key)
            
            for key in keys_to_remove:
                self.remove(key)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics."""
        stats = {
            'backend_type': self.backend_type,
            'default_ttl': self.default_ttl
        }
        
        # Add operation stats
        with self.stats_lock:
            stats.update(self.operation_stats)
        
        # Add backend-specific stats
        if hasattr(self.cache, 'stats'):
            stats.update(self.cache.stats())
        
        return stats
    
    def cleanup(self) -> None:
        """Cleanup expired entries (for LRU cache)."""
        if hasattr(self.cache, 'cleanup_expired'):
            removed_count = self.cache.cleanup_expired()
            self.logger.info(f"Cleaned up {removed_count} expired cache entries")
    
    def generate_key(self, *args, **kwargs) -> str:
        """Generate cache key from arguments."""
        # Create a deterministic key from arguments
        key_data = {
            'args': args,
            'kwargs': sorted(kwargs.items()) if kwargs else {}
        }
        
        # Serialize and hash
        key_str = json.dumps(key_data, sort_keys=True, default=str)
        return hashlib.md5(key_str.encode()).hexdigest()


# Global cache instance
_cache_service: Optional[PerformanceCacheService] = None

def get_cache_service() -> PerformanceCacheService:
    """Get global cache service instance."""
    global _cache_service
    if _cache_service is None:
        _cache_service = PerformanceCacheService()
    return _cache_service


def cached_result(ttl: int = 3600, key_prefix: str = "cached", 
                 cache_service: Optional[PerformanceCacheService] = None):
    """Decorator for caching function results."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Get cache service
            if cache_service is None:
                service = get_cache_service()
            else:
                service = cache_service
            
            # Generate cache key
            cache_key = f"{key_prefix}:{func.__name__}:{service.generate_key(*args, **kwargs)}"
            
            # Try to get from cache
            cached_value, found = service.get(cache_key)
            if found:
                return cached_value
            
            # Execute function
            result = func(*args, **kwargs)
            
            # Store in cache
            service.put(cache_key, result, ttl)
            
            return result
        
        # Add cache management methods to the wrapper
        wrapper.cache_clear = lambda: get_cache_service().invalidate_prefix(f"{key_prefix}:{func.__name__}")
        wrapper.cache_info = lambda: get_cache_service().get_stats()
        
        return wrapper
    return decorator


def invalidate_cache_group(group: str):
    """Invalidate all cached results for a specific group."""
    get_cache_service().invalidate_prefix(group)


# Cache warming utilities
class CacheWarmer:
    """Utility for warming up cache with commonly used data."""
    
    def __init__(self, cache_service: PerformanceCacheService):
        self.cache_service = cache_service
        self.logger = logging.getLogger(__name__)
    
    def warm_financial_models(self, common_assumptions: list):
        """Pre-calculate and cache common financial model results."""
        # This would be implemented based on your specific needs
        pass
    
    def warm_location_data(self, locations: list):
        """Pre-cache location comparison data."""
        # This would be implemented based on your specific needs
        pass


# Example usage for the financial service
def apply_caching_to_financial_service():
    """Apply caching decorators to financial service methods."""
    # This would be called during service initialization to add caching
    # to expensive financial calculations
    pass
