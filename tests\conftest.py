"""
Test Configuration
==================

Pytest configuration and fixtures for the test suite.
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import MagicMock

from ..models.client_profile import ClientProfile
from ..models.project_assumptions import EnhancedProjectAssumptions


@pytest.fixture
def temp_directory():
    """Create a temporary directory for testing."""
    temp_dir = tempfile.mkdtemp()
    yield Path(temp_dir)
    shutil.rmtree(temp_dir)


@pytest.fixture
def sample_client_profile():
    """Create a sample client profile for testing."""
    profile = ClientProfile()
    profile.company_name = "Test Solar Company"
    profile.client_name = "<PERSON>"
    profile.project_name = "Test Solar Project"
    profile.contact_email = "<EMAIL>"
    profile.phone = "******-123-4567"
    profile.project_location = "Ouarzazate, Morocco"
    profile.project_capacity_mw = 10.0
    profile.preferred_currency = "EUR"
    return profile


@pytest.fixture
def sample_project_assumptions():
    """Create sample project assumptions for testing."""
    assumptions = EnhancedProjectAssumptions()
    
    # Technical parameters
    assumptions.capacity_mw = 10.0
    assumptions.production_mwh_year1 = 18000.0
    assumptions.degradation_rate = 0.005
    assumptions.project_life_years = 25
    
    # Financial parameters
    assumptions.capex_meur = 8.5
    assumptions.opex_keuros_year1 = 180.0
    assumptions.ppa_price_eur_kwh = 0.045
    assumptions.ppa_escalation = 0.0
    assumptions.debt_ratio = 0.75
    assumptions.interest_rate = 0.06
    assumptions.debt_years = 15
    assumptions.discount_rate = 0.08
    assumptions.tax_rate = 0.30
    assumptions.land_lease_eur_mw_year = 2000.0
    
    # Grant parameters
    assumptions.grant_meur_italy = 0.0
    assumptions.grant_meur_masen = 0.0
    assumptions.grant_meur_connection = 0.0
    assumptions.grant_meur_simest_africa = 0.0
    
    return assumptions


@pytest.fixture
def sample_financial_results():
    """Create sample financial results for testing."""
    return {
        'kpis': {
            'IRR_project': 0.12,
            'IRR_equity': 0.15,
            'NPV_project': 5000000,
            'NPV_equity': 2000000,
            'LCOE_eur_kwh': 0.042,
            'Min_DSCR': 1.35,
            'Avg_DSCR': 1.45,
            'Payback_years': 8.5
        },
        'cashflow': {
            'Year': list(range(1, 26)),
            'Revenue': [810000] * 25,
            'OPEX': [-180000] * 25,
            'Capex': [-8500000] + [0] * 24,
            'Grants': [0] * 25,
            'Debt_Service': [0] + [-400000] * 15 + [0] * 9,
            'Equity_CF': [-2125000] + [500000] * 24,
            'Project_CF': [-8500000] + [630000] * 24,
            'DSCR': [0] + [1.4] * 15 + [0] * 9
        },
        'assumptions': {}  # Would contain the assumptions dict
    }


@pytest.fixture
def mock_page():
    """Create a mock Flet page for testing UI components."""
    page = MagicMock()
    page.overlay = []
    page.update = MagicMock()
    page.add = MagicMock()
    page.clean = MagicMock()
    page.show_snack_bar = MagicMock()
    return page


@pytest.fixture
def mock_file_system(temp_directory):
    """Create a mock file system structure for testing."""
    # Create directory structure
    data_dir = temp_directory / "data"
    charts_dir = temp_directory / "charts"
    reports_dir = temp_directory / "reports"
    
    data_dir.mkdir()
    charts_dir.mkdir()
    reports_dir.mkdir()
    
    # Create some test files
    test_data_file = data_dir / "test_data.json"
    test_data_file.write_text('{"test": "data"}')
    
    test_report_file = reports_dir / "test_report.xlsx"
    test_report_file.write_text("mock excel content")
    
    return {
        'base_dir': temp_directory,
        'data_dir': data_dir,
        'charts_dir': charts_dir,
        'reports_dir': reports_dir,
        'test_files': {
            'data': test_data_file,
            'report': test_report_file
        }
    }


@pytest.fixture
def mock_validation_results():
    """Create mock validation results for testing."""
    validation_result = MagicMock()
    validation_result.is_valid = True
    validation_result.warnings = []
    validation_result.errors = []
    validation_result.recommendations = []
    return validation_result


@pytest.fixture
def mock_benchmark_results():
    """Create mock benchmark results for testing."""
    return {
        'custom_analysis': {
            'irr_project': {
                'current': 0.12,
                'benchmark_min': 0.10,
                'benchmark_target': 0.12,
                'status': 'good'
            },
            'irr_equity': {
                'current': 0.15,
                'benchmark_min': 0.12,
                'benchmark_target': 0.15,
                'status': 'excellent'
            },
            'lcoe': {
                'current': 0.042,
                'benchmark_max': 0.050,
                'benchmark_target': 0.045,
                'status': 'excellent'
            },
            'dscr': {
                'current': 1.35,
                'benchmark_min': 1.20,
                'benchmark_target': 1.30,
                'status': 'good'
            }
        },
        'industry_benchmarks': {
            'solar_pv_morocco': {
                'irr_range': [0.08, 0.15],
                'lcoe_range': [0.035, 0.055],
                'capacity_factor_range': [0.18, 0.25]
            }
        }
    }


# Test configuration
def pytest_configure(config):
    """Configure pytest."""
    # Add custom markers
    config.addinivalue_line(
        "markers", "unit: mark test as a unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as an integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "ui: mark test as UI test"
    )


# Test collection configuration
def pytest_collection_modifyitems(config, items):
    """Modify test collection."""
    # Add markers based on test file names
    for item in items:
        if "test_integration" in item.nodeid:
            item.add_marker(pytest.mark.integration)
        elif "test_ui" in item.nodeid:
            item.add_marker(pytest.mark.ui)
        else:
            item.add_marker(pytest.mark.unit)
        
        # Mark slow tests
        if "monte_carlo" in item.name.lower() or "comprehensive" in item.name.lower():
            item.add_marker(pytest.mark.slow)
