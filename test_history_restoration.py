#!/usr/bin/env python3
"""
Test History Restoration Fix
============================

Test script to verify that the history service now properly restores all data,
including financial inputs, after the UI update fix.
"""

import sys
import os
import tempfile
import shutil
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models.client_profile import ClientProfile
from models.project_assumptions import EnhancedProjectAssumptions
from services.persistence_service import DataPersistenceService


def test_history_restoration():
    """Test that history restoration works correctly."""
    print("🧪 Testing History Restoration Fix...")
    
    # Create temporary database
    temp_dir = tempfile.mkdtemp()
    db_path = os.path.join(temp_dir, "test_projects.db")
    
    try:
        # Initialize persistence service
        persistence_service = DataPersistenceService(db_path)
        
        # Create test data
        print("📝 Creating test project data...")
        
        # Client profile with all fields
        client_profile = ClientProfile(
            company_name="Test Solar Company",
            client_name="<PERSON>",
            contact_email="<EMAIL>",
            phone="******-0123",
            project_name="Test Solar Project",
            project_location="Morocco",
            project_capacity_mw=25.5,
            preferred_currency="EUR"
        )
        
        # Project assumptions with financial inputs
        project_assumptions = EnhancedProjectAssumptions(
            technology_type="Solar PV",
            capacity_mw=25.5,
            production_mwh_year1=45000.0,
            degradation_rate=0.005,
            project_life_years=25,
            capex_meur=20.5,
            opex_keuros_year1=350.0,
            ppa_price_eur_kwh=0.055,
            ppa_escalation=0.02,
            debt_ratio=0.80,
            interest_rate=0.055,
            debt_years=18,
            discount_rate=0.085,
            tax_rate=0.32,
            land_lease_eur_mw_year=2500.0,
            grant_meur_italy=1.5,
            grant_meur_masen=2.0,
            grant_meur_connection=0.5,
            grant_meur_simest_africa=1.0
        )
        
        # Save project version
        print("💾 Saving project version...")
        project_id = client_profile.get_clean_company_name()
        project_data = {
            'name': client_profile.project_name,
            'client_profile': client_profile.to_dict(),
            'project_assumptions': project_assumptions.to_dict(),
            'financial_results': {'test': 'data'}
        }
        
        version_id = persistence_service.save_project_version(
            project_id, project_data, "Test version"
        )
        print(f"✅ Saved version: {version_id}")
        
        # Load project back
        print("📖 Loading project from history...")
        loaded_project = persistence_service.load_project(project_id, version=1)
        
        if not loaded_project:
            print("❌ Failed to load project from history")
            return False
        
        print("✅ Successfully loaded project from history")
        
        # Verify client profile data
        print("🔍 Verifying client profile restoration...")
        restored_client = ClientProfile.from_dict(loaded_project.client_profile)
        
        client_checks = [
            ("company_name", client_profile.company_name, restored_client.company_name),
            ("client_name", client_profile.client_name, restored_client.client_name),
            ("contact_email", client_profile.contact_email, restored_client.contact_email),
            ("phone", client_profile.phone, restored_client.phone),
            ("project_name", client_profile.project_name, restored_client.project_name),
            ("project_location", client_profile.project_location, restored_client.project_location),
            ("project_capacity_mw", client_profile.project_capacity_mw, restored_client.project_capacity_mw),
            ("preferred_currency", client_profile.preferred_currency, restored_client.preferred_currency)
        ]
        
        client_passed = True
        for field, original, restored in client_checks:
            if original != restored:
                print(f"❌ Client profile field '{field}': expected {original}, got {restored}")
                client_passed = False
            else:
                print(f"✅ Client profile field '{field}': {restored}")
        
        # Verify project assumptions (financial inputs)
        print("\n🔍 Verifying financial inputs restoration...")
        restored_assumptions = EnhancedProjectAssumptions.from_dict(loaded_project.project_assumptions)
        
        financial_checks = [
            ("capacity_mw", project_assumptions.capacity_mw, restored_assumptions.capacity_mw),
            ("production_mwh_year1", project_assumptions.production_mwh_year1, restored_assumptions.production_mwh_year1),
            ("capex_meur", project_assumptions.capex_meur, restored_assumptions.capex_meur),
            ("opex_keuros_year1", project_assumptions.opex_keuros_year1, restored_assumptions.opex_keuros_year1),
            ("ppa_price_eur_kwh", project_assumptions.ppa_price_eur_kwh, restored_assumptions.ppa_price_eur_kwh),
            ("ppa_escalation", project_assumptions.ppa_escalation, restored_assumptions.ppa_escalation),
            ("debt_ratio", project_assumptions.debt_ratio, restored_assumptions.debt_ratio),
            ("interest_rate", project_assumptions.interest_rate, restored_assumptions.interest_rate),
            ("debt_years", project_assumptions.debt_years, restored_assumptions.debt_years),
            ("discount_rate", project_assumptions.discount_rate, restored_assumptions.discount_rate),
            ("tax_rate", project_assumptions.tax_rate, restored_assumptions.tax_rate),
            ("land_lease_eur_mw_year", project_assumptions.land_lease_eur_mw_year, restored_assumptions.land_lease_eur_mw_year),
            ("grant_meur_italy", project_assumptions.grant_meur_italy, restored_assumptions.grant_meur_italy),
            ("grant_meur_masen", project_assumptions.grant_meur_masen, restored_assumptions.grant_meur_masen),
            ("grant_meur_connection", project_assumptions.grant_meur_connection, restored_assumptions.grant_meur_connection),
            ("grant_meur_simest_africa", project_assumptions.grant_meur_simest_africa, restored_assumptions.grant_meur_simest_africa)
        ]
        
        financial_passed = True
        for field, original, restored in financial_checks:
            if abs(float(original) - float(restored)) > 0.001:  # Allow for small floating point differences
                print(f"❌ Financial field '{field}': expected {original}, got {restored}")
                financial_passed = False
            else:
                print(f"✅ Financial field '{field}': {restored}")
        
        # Overall result
        print(f"\n📊 Test Results:")
        print(f"   Client Profile: {'✅ PASSED' if client_passed else '❌ FAILED'}")
        print(f"   Financial Inputs: {'✅ PASSED' if financial_passed else '❌ FAILED'}")
        
        overall_passed = client_passed and financial_passed
        print(f"\n🎯 Overall Result: {'✅ ALL TESTS PASSED' if overall_passed else '❌ SOME TESTS FAILED'}")
        
        if overall_passed:
            print("\n🎉 History restoration fix is working correctly!")
            print("   Both client profile and financial inputs are properly restored.")
        else:
            print("\n⚠️  History restoration still has issues.")
            print("   Please check the failed fields above.")
        
        return overall_passed
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Cleanup
        try:
            shutil.rmtree(temp_dir)
        except:
            pass


if __name__ == "__main__":
    print("🔧 Testing History Restoration Fix")
    print("=" * 50)
    
    success = test_history_restoration()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ History restoration fix verification PASSED")
        sys.exit(0)
    else:
        print("❌ History restoration fix verification FAILED")
        sys.exit(1)
