"""
Enhanced Financial Model Application
====================================

Main entry point for the enhanced financial modeling application.

Author: Abdelhalim Serhani
Company: Agevolami SRL
Website: www.agevolami.it & www.agevolami.ma
Tagline: Your way to explore crossborder opportunities and grow big
"""

import flet as ft
import logging
import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.app_controller import AppController
from config.app_config import AppConfig
from config.ui_config import UIConfig
from services.error_handler import setup_global_error_handling, global_error_handler


def setup_logging():
    """Setup application logging."""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "app.log"),
            logging.StreamHandler(sys.stdout)
        ]
    )


def create_security_screen(page: ft.Page, on_authenticated: callable):
    """Create modern security/authentication screen with 2025 design trends."""

    # Animation state
    login_in_progress = ft.Ref[bool]()
    login_in_progress.current = False

    def on_password_submit(e):
        password = password_field.value.strip()

        if not password:
            show_error("Please enter a password")
            return

        # Set loading state with modern animation
        set_loading_state(True)

        # Updated password for 2025
        if password == "agevolami2025":
            # Success animation
            success_animation()
            # Delay for animation then authenticate
            page.run_task(delayed_authenticate)
        else:
            # Error animation
            error_animation()
            set_loading_state(False)

    async def delayed_authenticate():
        """Delayed authentication for smooth animation."""
        import asyncio
        await asyncio.sleep(0.8)  # Allow success animation to complete
        on_authenticated()

    def on_password_change(e):
        error_text.visible = False
        error_text.update()

    def show_error(message: str):
        """Show error with modern styling."""
        error_text.value = message
        error_text.visible = True
        error_text.color = ft.Colors.RED_400
        error_text.update()

    def set_loading_state(loading: bool):
        """Set loading state with visual feedback."""
        login_in_progress.current = loading
        login_button.disabled = loading
        if loading:
            login_button.text = "Authenticating..."
            login_button.icon = ft.Icons.HOURGLASS_EMPTY
            progress_ring.visible = True
        else:
            login_button.text = "Access Application"
            login_button.icon = ft.Icons.LOGIN
            progress_ring.visible = False
        login_button.update()
        progress_ring.update()

    def success_animation():
        """Success animation with green checkmark."""
        password_field.bgcolor = ft.Colors.GREEN_50
        password_field.border_color = ft.Colors.GREEN_400
        password_field.update()

        success_icon.visible = True
        success_icon.update()

    def error_animation():
        """Error animation with red highlight."""
        password_field.bgcolor = ft.Colors.RED_50
        password_field.border_color = ft.Colors.RED_400
        password_field.update()

        # Reset after animation
        page.run_task(reset_field_styling)

    async def reset_field_styling():
        """Reset field styling after error."""
        import asyncio
        await asyncio.sleep(1)
        password_field.bgcolor = ft.Colors.WHITE
        password_field.border_color = ft.Colors.GREY_400
        password_field.update()

    # Modern password field with enhanced styling
    password_field = ft.TextField(
        label="Enter Access Password",
        password=True,
        autofocus=True,
        on_submit=on_password_submit,
        on_change=on_password_change,
        width=320,
        height=60,
        bgcolor=ft.Colors.WHITE,
        border_radius=12,
        border_color=ft.Colors.GREY_400,
        focused_border_color=ft.Colors.BLUE_600,
        cursor_color=ft.Colors.BLUE_600,
        text_style=ft.TextStyle(size=16),
        label_style=ft.TextStyle(color=ft.Colors.GREY_600)
    )

    # Modern error text
    error_text = ft.Text(
        "Invalid password. Please try again.",
        color=ft.Colors.RED_400,
        visible=False,
        size=14,
        weight=ft.FontWeight.W_500
    )

    # Success icon
    success_icon = ft.Icon(
        ft.Icons.CHECK_CIRCLE,
        color=ft.Colors.GREEN_400,
        size=24,
        visible=False
    )

    # Progress ring
    progress_ring = ft.ProgressRing(
        width=20,
        height=20,
        stroke_width=2,
        visible=False
    )

    # Modern login button with gradient effect
    login_button = ft.ElevatedButton(
        "Access Application",
        icon=ft.Icons.LOGIN,
        on_click=on_password_submit,
        width=320,
        height=56,
        style=ft.ButtonStyle(
            bgcolor=ft.Colors.BLUE_600,
            color=ft.Colors.WHITE,
            text_style=ft.TextStyle(size=16, weight=ft.FontWeight.W_600),
            shape=ft.RoundedRectangleBorder(radius=12),
            elevation=3,
            shadow_color=ft.Colors.BLUE_200,
        )
    )
    
    # Modern developer info with 2025 branding
    developer_info = ft.Container(
        content=ft.Column([
            # Modern header with gradient text effect
            ft.Container(
                content=ft.Column([
                    ft.Text("🚀 Enhanced Financial Model v3.0",
                           size=32, weight=ft.FontWeight.BOLD,
                           text_align=ft.TextAlign.CENTER,
                           color=ft.Colors.BLUE_800),
                    ft.Text("Advanced Solar PV Project Financial Analysis • 2025 Edition",
                           size=18, color=ft.Colors.GREY_700,
                           text_align=ft.TextAlign.CENTER,
                           weight=ft.FontWeight.W_500),
                ], spacing=8),
                padding=ft.padding.only(bottom=25)
            ),

            # Modern developer signature with enhanced styling
            ft.Container(
                content=ft.Column([
                    # Profile section
                    ft.Container(
                        content=ft.Row([
                            ft.Container(
                                content=ft.Icon(ft.Icons.ACCOUNT_CIRCLE,
                                              color=ft.Colors.BLUE_600, size=40),
                                bgcolor=ft.Colors.BLUE_50,
                                border_radius=25,
                                padding=8
                            ),
                            ft.Column([
                                ft.Text("Abdelhalim Serhani",
                                       size=20, weight=ft.FontWeight.BOLD,
                                       color=ft.Colors.GREY_800),
                                ft.Text("Senior Financial & Business Consultant",
                                       size=15, color=ft.Colors.GREY_600,
                                       weight=ft.FontWeight.W_500)
                            ], spacing=4)
                        ], alignment=ft.MainAxisAlignment.CENTER, spacing=15),
                        padding=ft.padding.only(bottom=20)
                    ),

                    # Company info with modern styling
                    ft.Container(
                        content=ft.Row([
                            ft.Icon(ft.Icons.BUSINESS_CENTER,
                                   color=ft.Colors.GREEN_600, size=24),
                            ft.Text("Agevolami SRL",
                                   size=16, weight=ft.FontWeight.BOLD,
                                   color=ft.Colors.GREEN_700)
                        ], alignment=ft.MainAxisAlignment.CENTER, spacing=10),
                        padding=ft.padding.only(bottom=15)
                    ),

                    # Tagline with modern typography
                    ft.Container(
                        content=ft.Text("Your gateway to cross-border opportunities and exponential growth",
                                       size=14, color=ft.Colors.BLUE_700,
                                       text_align=ft.TextAlign.CENTER,
                                       italic=True,
                                       weight=ft.FontWeight.W_500),
                        padding=ft.padding.only(bottom=20)
                    ),

                    # Website links with modern styling
                    ft.Row([
                        ft.Container(
                            content=ft.Row([
                                ft.Icon(ft.Icons.LANGUAGE,
                                       color=ft.Colors.ORANGE_600, size=18),
                                ft.Text("www.agevolami.it",
                                       size=13, color=ft.Colors.GREY_600,
                                       weight=ft.FontWeight.W_500)
                            ], spacing=6),
                            padding=8,
                            bgcolor=ft.Colors.ORANGE_50,
                            border_radius=8
                        ),
                        ft.Container(
                            content=ft.Row([
                                ft.Icon(ft.Icons.PUBLIC,
                                       color=ft.Colors.ORANGE_600, size=18),
                                ft.Text("www.agevolami.ma",
                                       size=13, color=ft.Colors.GREY_600,
                                       weight=ft.FontWeight.W_500)
                            ], spacing=6),
                            padding=8,
                            bgcolor=ft.Colors.ORANGE_50,
                            border_radius=8
                        )
                    ], alignment=ft.MainAxisAlignment.CENTER, spacing=10)
                ], spacing=5),
                padding=25,
                bgcolor=ft.Colors.WHITE,
                border_radius=16,
                border=ft.border.all(1, ft.Colors.GREY_200),
                shadow=ft.BoxShadow(
                    spread_radius=0,
                    blur_radius=20,
                    color=ft.Colors.GREY_300,
                    offset=ft.Offset(0, 4)
                )
            )
        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
        padding=30
    )
    
    # Modern security screen layout with 2025 design and scrolling
    security_content = ft.Container(
        content=ft.Column([
            # Scrollable content area
            ft.Container(
                content=ft.Column([
                    # Top spacing for better visual balance
                    ft.Container(height=20),
                    
                    # Developer info section
                    developer_info,

                    # Spacer with subtle animation potential
                    ft.Container(height=30),

                    # Modern login section with enhanced styling
                    ft.Container(
                        content=ft.Column([
                            # Security icon with modern styling
                            ft.Container(
                                        content=ft.Icon(ft.Icons.SHIELD, size=48, color=ft.Colors.BLUE_600),
                bgcolor=ft.Colors.BLUE_50,
                                border_radius=30,
                                padding=15,
                                margin=ft.margin.only(bottom=20)
                            ),

                            # Modern title
                            ft.Text("Secure Access Portal",
                                   size=24, weight=ft.FontWeight.BOLD,
                                   color=ft.Colors.GREY_800),

                            # Subtitle with better typography
                            ft.Text("Enter your credentials to access the financial modeling suite",
                                   size=15, color=ft.Colors.GREY_600,
                                   text_align=ft.TextAlign.CENTER,
                                   weight=ft.FontWeight.W_400),

                            # Spacer
                            ft.Container(height=25),

                            # Input field with success icon
                            ft.Stack([
                                password_field,
                                ft.Container(
                                    content=success_icon,
                                    right=15,
                                    top=18
                                )
                            ]),

                            # Error text with better positioning
                            ft.Container(
                                content=error_text,
                                height=25,
                                alignment=ft.alignment.center_left
                            ),

                            # Login button with progress indicator
                            ft.Stack([
                                login_button,
                                ft.Container(
                                    content=progress_ring,
                                    right=20,
                                    top=18
                                )
                            ]),

                            # Security note
                            ft.Container(
                                content=ft.Row([
                                    ft.Icon(ft.Icons.INFO_OUTLINE,
                                           color=ft.Colors.GREY_500, size=16),
                                    ft.Text("Secure authentication • Updated for 2025",
                                           size=12, color=ft.Colors.GREY_500,
                                           weight=ft.FontWeight.W_400)
                                ], alignment=ft.MainAxisAlignment.CENTER, spacing=8),
                                margin=ft.margin.only(top=20)
                            )
                        ], horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                        padding=35,
                        bgcolor=ft.Colors.WHITE,
                        border_radius=20,
                        border=ft.border.all(1, ft.Colors.GREY_200),
                        shadow=ft.BoxShadow(
                            spread_radius=0,
                            blur_radius=25,
                            color=ft.Colors.GREY_300,
                            offset=ft.Offset(0, 8)
                        ),
                        width=420,
                        alignment=ft.alignment.center
                    ),
                    
                    # Bottom spacing for better visual balance
                    ft.Container(height=40),
                    
                ], 
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                spacing=0,
                scroll=ft.ScrollMode.AUTO
                ),
                expand=True,
                padding=ft.padding.symmetric(horizontal=20)
            )
        ]),
        expand=True,
        bgcolor=ft.Colors.GREY_50,
        # Modern gradient background
        gradient=ft.LinearGradient(
            begin=ft.alignment.top_center,
            end=ft.alignment.bottom_center,
            colors=[ft.Colors.BLUE_50, ft.Colors.GREY_50]
        )
    )

    page.add(security_content)


def main(page: ft.Page):
    """Main application function."""
    
    # Load configuration
    app_config = AppConfig()
    ui_config = UIConfig()
    
    # Configure page
    page.title = f"{app_config.app_name} - {app_config.company_name}"
    page.theme_mode = ft.ThemeMode.LIGHT
    page.window_width = 1400
    page.window_height = 900
    page.window_min_width = 1200
    page.window_min_height = 800
    page.padding = 0
    
    def start_main_application():
        """Start the main application after authentication."""
        try:
            page.clean()

            # Initialize and start the main application controller
            app_controller = AppController(page)

            logging.info("Enhanced Financial Model Application started successfully")
        except Exception as e:
            global_error_handler.handle_error(e,
                context={'function': 'start_main_application'},
                show_user_message=True,
                page=page)
            logging.error(f"Failed to start main application: {str(e)}")
    
    # Show security screen first
    create_security_screen(page, start_main_application)


if __name__ == "__main__":
    # Setup logging and error handling
    setup_logging()
    setup_global_error_handling()

    # Log application startup
    logging.info("Starting Enhanced Financial Model Application v3.0 - 2025 Edition")
    logging.info("Author: Abdelhalim Serhani")
    logging.info("Company: Agevolami SRL")
    logging.info("Website: www.agevolami.it & www.agevolami.ma")
    logging.info("Enhanced with comprehensive error handling and DCF modeling")

    try:
        # Start the Flet application
        ft.app(
            target=main,
            name="Enhanced Financial Model v3.0",
            assets_dir="assets"
        )
    except Exception as e:
        global_error_handler.handle_error(e,
            context={'function': 'main_startup'},
            show_user_message=False)
        logging.critical(f"Critical failure starting application: {str(e)}")
        sys.exit(1)
