"""
Test Runner
===========

Script to run the test suite with different configurations.
"""

import sys
import subprocess
import argparse
from pathlib import Path


def run_unit_tests():
    """Run unit tests only."""
    cmd = [
        sys.executable, "-m", "pytest",
        "-v",
        "-m", "unit",
        "--tb=short",
        str(Path(__file__).parent)
    ]
    return subprocess.run(cmd)


def run_integration_tests():
    """Run integration tests only."""
    cmd = [
        sys.executable, "-m", "pytest",
        "-v",
        "-m", "integration",
        "--tb=short",
        str(Path(__file__).parent)
    ]
    return subprocess.run(cmd)


def run_all_tests():
    """Run all tests."""
    cmd = [
        sys.executable, "-m", "pytest",
        "-v",
        "--tb=short",
        str(Path(__file__).parent)
    ]
    return subprocess.run(cmd)


def run_tests_with_coverage():
    """Run tests with coverage report."""
    cmd = [
        sys.executable, "-m", "pytest",
        "-v",
        "--cov=src.new_app",
        "--cov-report=html",
        "--cov-report=term-missing",
        "--tb=short",
        str(Path(__file__).parent)
    ]
    return subprocess.run(cmd)


def run_fast_tests():
    """Run fast tests only (exclude slow tests)."""
    cmd = [
        sys.executable, "-m", "pytest",
        "-v",
        "-m", "not slow",
        "--tb=short",
        str(Path(__file__).parent)
    ]
    return subprocess.run(cmd)


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(description="Run Enhanced Financial Model tests")
    parser.add_argument(
        "--type",
        choices=["unit", "integration", "all", "fast"],
        default="all",
        help="Type of tests to run"
    )
    parser.add_argument(
        "--coverage",
        action="store_true",
        help="Run with coverage report"
    )
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Verbose output"
    )
    
    args = parser.parse_args()
    
    print("Enhanced Financial Model - Test Suite")
    print("=" * 50)
    
    if args.coverage:
        print("Running tests with coverage...")
        result = run_tests_with_coverage()
    elif args.type == "unit":
        print("Running unit tests...")
        result = run_unit_tests()
    elif args.type == "integration":
        print("Running integration tests...")
        result = run_integration_tests()
    elif args.type == "fast":
        print("Running fast tests...")
        result = run_fast_tests()
    else:
        print("Running all tests...")
        result = run_all_tests()
    
    print("\n" + "=" * 50)
    if result.returncode == 0:
        print("✅ All tests passed!")
    else:
        print("❌ Some tests failed!")
    
    return result.returncode


if __name__ == "__main__":
    sys.exit(main())
