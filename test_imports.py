#!/usr/bin/env python3
"""
Import Test Script
==================

Test script to verify all imports are working correctly.
"""

import sys
import traceback

def test_imports():
    """Test all critical imports."""
    print("Testing imports...")
    
    try:
        # Test core imports
        print("✓ Testing app imports...")
        from app.app_controller import AppController
        from app.app_state import AppState
        
        print("✓ Testing model imports...")
        from models.client_profile import ClientProfile
        from models.project_assumptions import EnhancedProjectAssumptions
        from models.ui_state import UIState, TabState
        from models.location_config import LocationConfig, LocationManager
        
        print("✓ Testing service imports...")
        from services.financial_service import FinancialModelService
        from services.validation_service import ValidationService
        from services.export_service import ExportService
        from services.location_service import LocationComparisonService
        from services.report_service import ReportGenerationService
        
        print("✓ Testing view imports...")
        from views.base_view import BaseView
        from views.project_setup_view import ProjectSetupView
        from views.dashboard_view import DashboardView
        from views.location_comparison_view import LocationComparisonView
        from views.financial_model_view import FinancialModelView
        from views.validation_view import ValidationView
        from views.sensitivity_view import SensitivityView
        from views.monte_carlo_view import MonteCarloView
        from views.scenarios_view import ScenariosView
        from views.export_view import ExportView
        
        print("✓ Testing component imports...")
        from components.charts.kpi_charts import KPICharts
        from components.charts.cashflow_charts import CashflowCharts
        from components.charts.comparison_charts import ComparisonCharts
        from components.widgets.kpi_card import KPICard
        from components.forms.client_profile_form import ClientProfileForm
        from components.forms.project_params_form import ProjectParamsForm
        
        print("✓ Testing config imports...")
        from config.app_config import AppConfig
        from config.ui_config import UIConfig
        from config.export_config import ExportConfig
        
        print("✓ Testing utility imports...")
        from utils.file_utils import FileUtils
        from utils.validation_utils import ValidationUtils
        from utils.formatting_utils import FormattingUtils
        
        print("\n🎉 All imports successful!")
        return True
        
    except ImportError as e:
        print(f"\n❌ Import error: {e}")
        print(f"Traceback: {traceback.format_exc()}")
        return False
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_imports()
    sys.exit(0 if success else 1)
