"""
Quality Assurance Validator
===========================

Comprehensive validation system for the enhanced reporting capabilities.
"""

import sys
import os
from pathlib import Path
import traceback
import tempfile
import shutil
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class QAValidator:
    """Quality assurance validator for the reporting system."""
    
    def __init__(self):
        self.test_results = []
        self.temp_dir = None
        
    def setup_test_environment(self):
        """Set up test environment."""
        self.temp_dir = Path(tempfile.mkdtemp())
        print(f"📁 Test environment created: {self.temp_dir}")
        
    def cleanup_test_environment(self):
        """Clean up test environment."""
        if self.temp_dir and self.temp_dir.exists():
            shutil.rmtree(self.temp_dir, ignore_errors=True)
            print("🧹 Test environment cleaned up")
    
    def run_test(self, test_name, test_func):
        """Run a single test and record results."""
        print(f"\n🧪 Running: {test_name}")
        try:
            result = test_func()
            if result:
                print(f"✅ PASSED: {test_name}")
                self.test_results.append((test_name, True, None))
                return True
            else:
                print(f"❌ FAILED: {test_name}")
                self.test_results.append((test_name, False, "Test returned False"))
                return False
        except Exception as e:
            print(f"❌ ERROR: {test_name} - {str(e)}")
            self.test_results.append((test_name, False, str(e)))
            return False
    
    def test_imports(self):
        """Test all critical imports."""
        try:
            # Core services
            from services.report_service import ReportGenerationService
            from services.export_service import ExportService
            from components.charts.chart_factory import ChartFactory
            
            # Models
            from models.client_profile import ClientProfile
            from models.project_assumptions import EnhancedProjectAssumptions
            
            # Optional dependencies
            try:
                import seaborn as sns
                print("  ✅ Seaborn available")
            except ImportError:
                print("  ⚠️ Seaborn not available")
            
            try:
                import plotly.graph_objects as go
                print("  ✅ Plotly available")
            except ImportError:
                print("  ⚠️ Plotly not available")
            
            try:
                from reportlab.lib.pagesizes import A4
                print("  ✅ ReportLab available")
            except ImportError:
                print("  ⚠️ ReportLab not available")
            
            try:
                from pptx import Presentation
                print("  ✅ python-pptx available")
            except ImportError:
                print("  ⚠️ python-pptx not available")
            
            return True
        except Exception as e:
            print(f"  ❌ Import failed: {e}")
            return False
    
    def test_chart_factory_initialization(self):
        """Test chart factory can be initialized."""
        try:
            from components.charts.chart_factory import ChartFactory
            
            chart_factory = ChartFactory()
            
            # Check professional styling
            if not hasattr(chart_factory, 'professional_colors'):
                print("  ⚠️ Professional colors not found")
                return False
            
            if not hasattr(chart_factory, 'professional_style'):
                print("  ⚠️ Professional style not found")
                return False
            
            print(f"  ✅ Professional color schemes: {len(chart_factory.professional_colors)}")
            print(f"  ✅ Professional styling configured")
            
            return True
        except Exception as e:
            print(f"  ❌ Chart factory initialization failed: {e}")
            return False
    
    def test_basic_chart_creation(self):
        """Test basic chart creation."""
        try:
            from components.charts.chart_factory import ChartFactory
            
            chart_factory = ChartFactory()
            
            # Test bar chart
            test_data = {'IRR': 12.5, 'NPV': 15.2, 'DSCR': 1.8, 'LCOE': 4.2}
            ui_component, chart_bytes = chart_factory.create_and_export_bar_chart(
                data=test_data,
                title='QA Test Chart'
            )
            
            if not chart_bytes or len(chart_bytes) < 1000:
                print("  ❌ Chart bytes too small or empty")
                return False
            
            print(f"  ✅ Bar chart created: {len(chart_bytes)} bytes")
            return True
            
        except Exception as e:
            print(f"  ❌ Basic chart creation failed: {e}")
            return False
    
    def test_advanced_charts(self):
        """Test advanced chart functionality."""
        try:
            from components.charts.chart_factory import ChartFactory
            import pandas as pd
            import numpy as np
            
            chart_factory = ChartFactory()
            charts_created = 0
            
            # Test sensitivity heatmap
            if hasattr(chart_factory, 'create_sensitivity_heatmap'):
                sensitivity_df = pd.DataFrame(
                    np.random.randn(3, 3),
                    index=['CAPEX', 'OPEX', 'Tariff'],
                    columns=['-10%', '0%', '+10%']
                )
                ui_component, chart_bytes = chart_factory.create_sensitivity_heatmap(
                    sensitivity_data=sensitivity_df
                )
                if chart_bytes:
                    charts_created += 1
                    print("  ✅ Sensitivity heatmap")
            
            # Test tornado diagram
            if hasattr(chart_factory, 'create_tornado_diagram'):
                tornado_data = {
                    'CAPEX': {'low': -15, 'high': 12},
                    'OPEX': {'low': -8, 'high': 10}
                }
                ui_component, chart_bytes = chart_factory.create_tornado_diagram(
                    sensitivity_data=tornado_data
                )
                if chart_bytes:
                    charts_created += 1
                    print("  ✅ Tornado diagram")
            
            # Test Monte Carlo distribution
            if hasattr(chart_factory, 'create_monte_carlo_distribution'):
                simulation_results = np.random.normal(10000000, 2000000, 100)
                ui_component, chart_bytes = chart_factory.create_monte_carlo_distribution(
                    simulation_results=simulation_results
                )
                if chart_bytes:
                    charts_created += 1
                    print("  ✅ Monte Carlo distribution")
            
            print(f"  ✅ Advanced charts created: {charts_created}")
            return charts_created >= 2
            
        except Exception as e:
            print(f"  ❌ Advanced charts test failed: {e}")
            return False
    
    def test_export_services(self):
        """Test export service functionality."""
        try:
            from services.export_service import ExportService
            from models.client_profile import ClientProfile
            from models.project_assumptions import EnhancedProjectAssumptions
            
            export_service = ExportService()
            
            # Create test data
            client_profile = ClientProfile(
                company_name="QA Test Corp",
                project_name="QA Test Project",
                consultant="QA Tester"
            )
            
            assumptions = EnhancedProjectAssumptions(
                capacity_mw=50.0,
                capex_meur=40.0
            )
            
            financial_results = {
                'kpis': {
                    'IRR_project': 0.12,
                    'NPV_project': 10000000,
                    'LCOE_eur_kwh': 0.045,
                    'Min_DSCR': 1.3
                }
            }
            
            output_dir = {'reports_dir': self.temp_dir}
            
            # Test JSON export (most basic)
            json_file = export_service.export_json_data(
                client_profile=client_profile,
                assumptions=assumptions,
                financial_results=financial_results,
                output_dir=output_dir
            )
            
            if not json_file.exists():
                print("  ❌ JSON export failed")
                return False
            
            print("  ✅ JSON export successful")
            
            # Test HTML export
            html_file = export_service.export_html_report(
                client_profile=client_profile,
                assumptions=assumptions,
                financial_results=financial_results,
                output_dir=output_dir
            )
            
            if not html_file.exists():
                print("  ❌ HTML export failed")
                return False
            
            print("  ✅ HTML export successful")
            return True
            
        except Exception as e:
            print(f"  ❌ Export services test failed: {e}")
            return False
    
    def test_report_service(self):
        """Test report service functionality."""
        try:
            from services.report_service import ReportGenerationService
            
            report_service = ReportGenerationService()
            
            # Test executive summary generation
            sample_results = {
                'financial': {
                    'kpis': {
                        'IRR_project': 0.125,
                        'NPV_project': 15000000,
                        'LCOE_eur_kwh': 0.042,
                        'Min_DSCR': 1.35
                    }
                }
            }
            
            summary = report_service.generate_executive_summary(sample_results)
            
            required_sections = [
                "EXECUTIVE SUMMARY",
                "KEY FINANCIAL PERFORMANCE",
                "INVESTMENT RECOMMENDATION"
            ]
            
            for section in required_sections:
                if section not in summary:
                    print(f"  ❌ Missing section: {section}")
                    return False
            
            print("  ✅ Executive summary with all sections")
            return True
            
        except Exception as e:
            print(f"  ❌ Report service test failed: {e}")
            return False
    
    def run_all_tests(self):
        """Run all quality assurance tests."""
        print("🚀 COMPREHENSIVE QUALITY ASSURANCE VALIDATION")
        print("=" * 60)
        
        self.setup_test_environment()
        
        tests = [
            ("Import Validation", self.test_imports),
            ("Chart Factory Initialization", self.test_chart_factory_initialization),
            ("Basic Chart Creation", self.test_basic_chart_creation),
            ("Advanced Charts", self.test_advanced_charts),
            ("Export Services", self.test_export_services),
            ("Report Service", self.test_report_service)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            if self.run_test(test_name, test_func):
                passed += 1
        
        self.cleanup_test_environment()
        
        print(f"\n{'='*60}")
        print(f"🎯 QUALITY ASSURANCE RESULTS: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 ALL QUALITY ASSURANCE TESTS PASSED!")
            print("\n✅ SYSTEM VALIDATION COMPLETE")
            print("✅ Enhanced reporting system is production-ready!")
        else:
            print(f"⚠️ {total - passed} tests failed")
            print("❌ System requires attention before production use")
        
        return passed == total


def main():
    """Main QA validation entry point."""
    validator = QAValidator()
    success = validator.run_all_tests()
    
    if success:
        print("\n🎊 CONGRATULATIONS! 🎊")
        print("Your enhanced reporting system has passed all quality checks!")
        print("\n📈 SYSTEM CAPABILITIES VALIDATED:")
        print("   • Professional chart generation")
        print("   • Multiple export formats")
        print("   • Advanced risk analysis")
        print("   • Executive summary generation")
        print("   • Error handling and robustness")
    
    return success


if __name__ == "__main__":
    main()
