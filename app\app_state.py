"""
Application State Management
============================

Centralized state management for the application.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional
import pandas as pd
from datetime import datetime

from models.client_profile import ClientProfile
from models.project_assumptions import EnhancedProjectAssumptions


@dataclass
class AppState:
    """Centralized application state."""
    
    # Core data
    client_profile: ClientProfile = field(default_factory=ClientProfile)
    project_assumptions: EnhancedProjectAssumptions = field(default_factory=EnhancedProjectAssumptions)
    
    # Analysis results
    financial_results: Optional[Dict[str, Any]] = None
    validation_results: Optional[Any] = None
    benchmark_results: Optional[Dict[str, Any]] = None
    location_comparison_results: Optional[Dict[str, Any]] = None
    sensitivity_results: Optional[pd.DataFrame] = None
    monte_carlo_results: Optional[Dict[str, Any]] = None
    scenario_results: Optional[Dict[str, Any]] = None
    
    # Enhanced features results
    ml_predictions: Optional[Dict[str, Any]] = None
    charts_3d: Optional[Dict[str, str]] = None
    performance_stats: Optional[Dict[str, Any]] = None
    undo_redo_history: Optional[Dict[str, Any]] = None
    
    # Application metadata
    last_saved: Optional[str] = None
    last_model_run: Optional[str] = None
    app_version: str = "2.0.0"
    
    def __post_init__(self):
        """Initialize state after creation."""
        if not self.client_profile:
            self.client_profile = ClientProfile()
        if not self.project_assumptions:
            self.project_assumptions = EnhancedProjectAssumptions()
    
    def has_financial_results(self) -> bool:
        """Check if financial results are available."""
        return self.financial_results is not None
    
    def has_validation_results(self) -> bool:
        """Check if validation results are available."""
        return self.validation_results is not None
    
    def has_location_comparison(self) -> bool:
        """Check if location comparison results are available."""
        return self.location_comparison_results is not None
    
    def has_sensitivity_results(self) -> bool:
        """Check if sensitivity analysis results are available."""
        return self.sensitivity_results is not None
    
    def has_monte_carlo_results(self) -> bool:
        """Check if Monte Carlo results are available."""
        return self.monte_carlo_results is not None
    
    def has_scenario_results(self) -> bool:
        """Check if scenario analysis results are available."""
        return self.scenario_results is not None
    
    def has_ml_predictions(self) -> bool:
        """Check if ML predictions are available."""
        return self.ml_predictions is not None
    
    def has_3d_charts(self) -> bool:
        """Check if 3D charts are available."""
        return self.charts_3d is not None
    
    def clear_all_results(self):
        """Clear all analysis results."""
        self.financial_results = None
        self.validation_results = None
        self.benchmark_results = None
        self.location_comparison_results = None
        self.sensitivity_results = None
        self.monte_carlo_results = None
        self.scenario_results = None
        self.ml_predictions = None
        self.charts_3d = None
        self.performance_stats = None
        self.undo_redo_history = None
        self.last_model_run = None
    
    def mark_model_run(self):
        """Mark that the model has been run."""
        self.last_model_run = datetime.now().isoformat()
    
    def mark_saved(self):
        """Mark that the state has been saved."""
        self.last_saved = datetime.now().isoformat()
    
    def get_analysis_summary(self) -> Dict[str, bool]:
        """Get summary of available analysis results."""
        return {
            'financial_model': self.has_financial_results(),
            'validation': self.has_validation_results(),
            'location_comparison': self.has_location_comparison(),
            'sensitivity_analysis': self.has_sensitivity_results(),
            'monte_carlo': self.has_monte_carlo_results(),
            'scenario_analysis': self.has_scenario_results()
        }
    
    def get_completion_percentage(self) -> float:
        """Get completion percentage of analysis."""
        summary = self.get_analysis_summary()
        completed = sum(summary.values())
        total = len(summary)
        return (completed / total) * 100 if total > 0 else 0
    
    def is_ready_for_export(self) -> bool:
        """Check if state is ready for export."""
        return (self.client_profile.is_complete() and 
                self.project_assumptions.is_validated and 
                self.has_financial_results())
    
    def get_export_data(self) -> Dict[str, Any]:
        """Get data ready for export."""
        return {
            'client_profile': self.client_profile.to_dict(),
            'project_assumptions': self.project_assumptions.to_dict(),
            'financial_results': self.financial_results,
            'validation_results': self.validation_results,
            'location_comparison_results': self.location_comparison_results,
            'sensitivity_results': self.sensitivity_results.to_dict('records') if self.sensitivity_results is not None else None,
            'monte_carlo_results': self.monte_carlo_results,
            'scenario_results': self.scenario_results,
            'metadata': {
                'last_saved': self.last_saved,
                'last_model_run': self.last_model_run,
                'app_version': self.app_version,
                'export_timestamp': datetime.now().isoformat()
            }
        }
    
    def load_from_dict(self, data: Dict[str, Any]):
        """Load state from dictionary."""
        if 'client_profile' in data:
            self.client_profile = ClientProfile.from_dict(data['client_profile'])
        
        if 'project_assumptions' in data:
            self.project_assumptions = EnhancedProjectAssumptions.from_dict(data['project_assumptions'])
        
        if 'financial_results' in data:
            self.financial_results = data['financial_results']
        
        if 'validation_results' in data:
            self.validation_results = data['validation_results']
        
        if 'location_comparison_results' in data:
            self.location_comparison_results = data['location_comparison_results']
        
        if 'sensitivity_results' in data and data['sensitivity_results']:
            self.sensitivity_results = pd.DataFrame(data['sensitivity_results'])
        
        if 'monte_carlo_results' in data:
            self.monte_carlo_results = data['monte_carlo_results']
        
        if 'scenario_results' in data:
            self.scenario_results = data['scenario_results']
        
        # Load metadata
        metadata = data.get('metadata', {})
        self.last_saved = metadata.get('last_saved')
        self.last_model_run = metadata.get('last_model_run')
        self.app_version = metadata.get('app_version', self.app_version)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert state to dictionary."""
        return self.get_export_data()
    
    def validate_state(self) -> Dict[str, str]:
        """Validate current state and return any issues."""
        issues = {}
        
        # Validate client profile
        client_errors = self.client_profile.validate()
        if client_errors:
            issues['client_profile'] = f"Client profile issues: {', '.join(client_errors.values())}"
        
        # Validate project assumptions
        if not self.project_assumptions.is_validated:
            assumption_errors = self.project_assumptions.validation_errors
            if assumption_errors:
                issues['project_assumptions'] = f"Project assumptions issues: {', '.join(assumption_errors.values())}"
        
        return issues
    
    def get_state_info(self) -> Dict[str, Any]:
        """Get information about current state."""
        return {
            'client_name': self.client_profile.get_display_name(),
            'project_name': self.client_profile.project_name,
            'capacity_mw': self.project_assumptions.capacity_mw,
            'location': getattr(self.project_assumptions, 'location_name', 'Not specified'),
            'last_model_run': self.last_model_run,
            'analysis_completion': self.get_completion_percentage(),
            'ready_for_export': self.is_ready_for_export(),
            'validation_status': self.project_assumptions.is_validated
        }
